#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
航班重排班模块 - 完全基于predict.py的统一实现
基于PPO强化学习算法进行航班重排班优化
100%遵循predict.py中的实现逻辑
"""

import pandas as pd
import numpy as np
import os
import torch
import torch.nn as nn
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings("ignore", category=FutureWarning)

# ============= 延误成本计算函数（与predict.py完全一致）=============
def delay_cost_caculate(delay, passenger_count):
    return np.where(delay < 240, 0,
                    np.where(delay <= 480, 200 * passenger_count,
                             400 * passenger_count)).astype(int)

# ============= 时隙管理函数（与predict.py完全一致）=============
def generate_time_slots(window_start, window_end, slot_length=5):
    slots = []
    t = window_start
    while t < window_end:
        slot_end = t + timedelta(minutes=slot_length)
        slots.append({'slot_start': t, 'slot_end': slot_end})
        t = slot_end
    return slots

def get_slot_index_for_time(slots, dt):
    for idx, slot in enumerate(slots):
        if slot['slot_start'] <= dt < slot['slot_end']:
            return idx
    return None

def filter_available_slots(df, window_start, window_end, slot_length=5):
    slots = generate_time_slots(window_start, window_end, slot_length)
    available = [False] * len(slots)
    for idx, row in df.iterrows():
        sch_dep = row['scheduled_departure']
        slot_idx = get_slot_index_for_time(slots, sch_dep)
        if slot_idx is not None:
            available[slot_idx] = True
    return available

def build_global_slot_index(slots_per_window):
    slot_key2idx = dict()
    idx2slot = dict()
    idx = 0
    for slots in slots_per_window:
        for slot in slots:
            key = (slot['slot_start'], slot['slot_end'])
            if key not in slot_key2idx:
                slot_key2idx[key] = idx
                idx2slot[idx] = slot
                idx += 1
    return slot_key2idx, idx2slot

class FlightRescheduler:
    """航班重排班器 - 100%基于predict.py的实现"""

    def __init__(self):
        self.model_path = os.path.join(os.path.dirname(__file__), '..', 'model', 'ppo_model.pt')
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self._initialize_model()

    def _initialize_model(self):
        """初始化PPO模型（与predict.py完全一致）"""
        try:
            # 模型参数（与predict.py保持一致）
            batch_flight_size = 46
            slot_count = 72
            state_dim = batch_flight_size + slot_count
            action_dim = batch_flight_size * (slot_count + 1)

            self.model = MaskedActorCritic(state_dim, action_dim).to(self.device)

            if os.path.exists(self.model_path):
                self.model.load_state_dict(torch.load(self.model_path, map_location=self.device))
                self.model.eval()
                print(f"成功加载重排班模型: {self.model_path}")
            else:
                print(f"警告: 未找到模型文件 {self.model_path}")

        except Exception as e:
            print(f"重排班模型初始化失败: {e}")
            self.model = None
    
    def process_delay_prediction_data(self, delay_prediction_df):
        """
        将延误预测结果转换为重排班算法需要的格式（完全按照predict.py的数据格式）

        Args:
            delay_prediction_df: 延误预测结果DataFrame

        Returns:
            DataFrame: 转换后的数据
        """
        try:
            # 创建重排班算法需要的数据格式（与predict.py的FlightDataLoader.load_data()一致）
            rescheduler_data = []

            for _, row in delay_prediction_df.iterrows():
                # 确保时间格式正确
                scheduled_dep = self._ensure_datetime_format(row.get('计划离港时间', ''))
                actual_dep = self._ensure_datetime_format(row.get('实际离港时间', ''))
                predicted_dep = self._ensure_datetime_format(row.get('预测离港时间', ''))

                # 如果没有预测时间，使用实际时间；如果没有实际时间，使用计划时间
                if not predicted_dep:
                    predicted_dep = actual_dep if actual_dep else scheduled_dep
                if not actual_dep:
                    actual_dep = predicted_dep if predicted_dep else scheduled_dep

                rescheduler_data.append({
                    'flight_id': str(row.get('航班号', '')),
                    'scheduled_departure': scheduled_dep,
                    'actual_departure': actual_dep,
                    'expected_departure': predicted_dep,
                    'passenger_count': 150,  # 设置合理的默认乘客数
                    'passenger_count_max': 180,  # 设置合理的最大乘客数
                    'arrival_place': ''  # 暂时为空，后续可扩展
                })

            df = pd.DataFrame(rescheduler_data)

            # 确保所有必需的列都存在（与predict.py的FlightDataLoader.load_data()一致）
            cols_needed = ['flight_id', 'scheduled_departure', 'actual_departure', 'expected_departure',
                          'passenger_count', 'passenger_count_max', 'arrival_place']
            for col in cols_needed:
                if col not in df.columns:
                    if col == 'passenger_count_max':
                        df[col] = 180
                    elif col == 'passenger_count':
                        df[col] = 150
                    elif col == 'arrival_place':
                        df[col] = ""
                    else:
                        df[col] = None

            print(f"延误预测数据转换完成: {len(df)} 条记录")
            return df

        except Exception as e:
            print(f"数据转换失败: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()

    def _ensure_datetime_format(self, time_value):
        """确保时间格式为datetime对象"""
        if pd.isna(time_value) or time_value == '':
            return None

        if isinstance(time_value, str):
            try:
                # 尝试解析不同的时间格式
                if len(time_value) == 5:  # HH:MM格式
                    # 假设是当天的时间，需要添加日期
                    today = datetime.now().strftime('%Y-%m-%d')
                    time_value = f"{today} {time_value}:00"
                return pd.to_datetime(time_value)
            except:
                return None
        elif isinstance(time_value, datetime):
            return time_value
        else:
            try:
                return pd.to_datetime(time_value)
            except:
                return None
    
    def optimize_schedule(self, delay_prediction_df, target_date):
        """
        执行航班重排班优化（完全基于predict.py的whole_day_predict函数）

        Args:
            delay_prediction_df: 延误预测结果DataFrame
            target_date: 目标日期

        Returns:
            tuple: (原始数据DataFrame, 优化结果DataFrame)
        """
        if self.model is None:
            raise ValueError("重排班模型未正确初始化")

        # 转换数据格式
        processed_data = self.process_delay_prediction_data(delay_prediction_df)

        if processed_data.empty:
            raise ValueError("没有有效的延误预测数据")

        try:
            # 调用核心优化算法（完全基于predict.py的whole_day_predict）
            original_df, optimized_df = self.whole_day_predict(processed_data)

            print(f"重排班优化完成: 原始 {len(original_df)} 条，优化后 {len(optimized_df) if optimized_df is not None else 0} 条")

            return original_df, optimized_df

        except Exception as e:
            print(f"重排班优化失败: {e}")
            import traceback
            traceback.print_exc()
            return processed_data, None
    
    def whole_day_predict(self, df):
        """
        全天预测功能（完全基于predict.py的whole_day_predict函数）
        """
        # 算法参数（与predict.py完全一致）
        slot_length = 5
        max_per_slot = 5
        batch_flight_size = 46
        slot_count = 72
        allow_cancel = False

        # 准备数据（与predict.py完全一致）
        df['date_only'] = df['scheduled_departure'].dt.strftime('%Y-%m-%d')
        date0 = df['date_only'].min()
        day_start = datetime.strptime(date0 + ' 00:00:00', '%Y-%m-%d %H:%M:%S')

        # 生成时间窗口（与predict.py完全一致）
        windows = get_time_windows_for_whole_day(day_start, slot_count=slot_count, slot_length=slot_length)

        # 生成时隙（与predict.py完全一致）
        slots_per_window = []
        for window_start, window_end in windows:
            slots = generate_time_slots(window_start, window_end, slot_length)
            slots_per_window.append(slots)
        global_slot_key2idx, idx2slot = build_global_slot_index(slots_per_window)
        global_slot_counts = [0] * len(idx2slot)

        assigned_flight_ids = set()
        all_results = []

        # 处理每个时间窗口（与predict.py完全一致）
        for i, (window_start, window_end) in enumerate(windows):
            flights = df[
                (df['scheduled_departure'] >= window_start) &
                (df['scheduled_departure'] < window_end) &
                (~df['flight_id'].isin(assigned_flight_ids))
            ].copy()

            slots = slots_per_window[i]
            slots, slot_real_mask = pad_slots_to_count(slots, slot_count, window_end)
            available_slots = filter_available_slots(df, window_start, window_end, slot_length)
            available_slots = list(available_slots) + [False]*(slot_count-len(available_slots))
            slot_occupied_counts = [0 for _ in range(slot_count)]

            if len(flights) == 0:
                continue

            # 调用批处理预测（与predict.py完全一致）
            res_df, _ = batch_predict(
                self.model, self.model_path, flights, slots, available_slots, slot_occupied_counts,
                slot_length, max_per_slot,
                batch_flight_size=batch_flight_size, slot_count=slot_count, device=self.device,
                slot_real_mask=slot_real_mask,
                global_slot_key2idx=global_slot_key2idx,
                global_slot_counts=global_slot_counts,
                allow_cancel=allow_cancel,
                use_expected_depature=True
            )

            res_df['window_start'] = window_start
            res_df['window_end'] = window_end
            all_results.append(res_df)
            assigned_flight_ids.update(res_df['flight_id'].unique())

        # 合并所有结果（与predict.py完全一致）
        if len(all_results) == 0:
            print("全天没有任何航班。")
            return df, pd.DataFrame()

        final_assignment = pd.concat(all_results, ignore_index=True)
        final_assignment = final_assignment.reset_index(drop=True)
        final_assignment = final_assignment.drop_duplicates(subset=['flight_id'], keep='last').reset_index(drop=True)

        # 合并航班功能（与predict.py完全一致）
        merged_assignment = merge_flights(final_assignment)

        # 转换为显示格式
        result_df = self._convert_to_display_format(merged_assignment)

        return df, result_df
    
# ============= 时间窗口生成（与predict.py完全一致）=============
def get_time_windows_for_whole_day(start_time, slot_count=72, slot_length=5):
    windows = []
    cur = start_time
    day_end = (start_time.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1))
    while cur < day_end:
        window_start = cur
        window_end = window_start + timedelta(minutes=slot_length*slot_count)
        if window_end > day_end:
            window_end = day_end
        windows.append((window_start, window_end))
        cur = window_start + timedelta(minutes=slot_length*int(slot_count/2))  # 滑窗
    return windows

# ============= 批处理和填充功能（与predict.py完全一致）=============
def pad_flights_to_batch(flights_df, batch_size, slot_count):
    n = len(flights_df)
    if n >= batch_size:
        df = flights_df.iloc[:batch_size].copy()
        real_mask = np.ones(batch_size, dtype=bool)
        return df, real_mask
    else:
        df_real = flights_df.copy()
        virtual_rows = []
        max_sch_dep = df_real['scheduled_departure'].max() if not df_real.empty else datetime.now()

        for i in range(batch_size - n):
            virtual_row = {
                'flight_id': f'virtual_{i}',
                'scheduled_departure': max_sch_dep + timedelta(minutes=5*(i+1)),
                'actual_departure': max_sch_dep + timedelta(minutes=10*(i+1)),
                'expected_departure': max_sch_dep + timedelta(minutes=10*(i+1)),
                'passenger_count': 0,
                'passenger_count_max': 0,
                'arrival_place': ''
            }
            virtual_rows.append(virtual_row)
        df_virtual = pd.DataFrame(virtual_rows)
        df = pd.concat([df_real, df_virtual], ignore_index=True)
        real_mask = np.array([True]*n + [False]*(batch_size-n))
        return df, real_mask

def pad_slots_to_count(slots, slot_count, window_end):
    n = len(slots)
    if n >= slot_count:
        return slots, np.ones(slot_count, dtype=bool)
    else:
        virtual_slots = []
        t = slots[-1]['slot_end'] if n > 0 else window_end
        for i in range(slot_count - n):
            slot_start = t + timedelta(minutes=5*i)
            slot_end = slot_start + timedelta(minutes=5)
            virtual_slots.append({'slot_start': slot_start, 'slot_end': slot_end})
        all_slots = slots + virtual_slots
        real_mask = np.array([True]*n + [False]*(slot_count-n))
        return all_slots, real_mask

def get_action_mask_with_virtuals(env, flight_real_mask, slot_real_mask):
    n_flight, n_slot = len(env.flights_df), len(env.slots)
    mask = env.get_action_mask()
    for i, is_real_flight in enumerate(flight_real_mask):
        if not is_real_flight:
            mask[i, :] = False
    for j, is_real_slot in enumerate(slot_real_mask):
        if not is_real_slot:
            mask[:, j] = False
    return mask

# ============= 神经网络模型辅助函数（与predict.py完全一致）=============
def flatten_state(state):
    return np.array(state[0] + state[1], dtype=np.float32)

def decode_action(action_idx, n_flight, n_slot):
    slot_plus = n_slot + 1
    flight_idx = action_idx // slot_plus
    slot_idx = action_idx % slot_plus
    return (flight_idx, slot_idx)

    def _batch_predict(self, agent, flights_df, slots, available_slots, slot_occupied_counts,
                      slot_length, max_per_slot, batch_flight_size=46, slot_count=72,
                      slot_real_mask=None, global_slot_key2idx=None, global_slot_counts=None,
                      allow_cancel=True, use_expected_departure=False):
        """批处理预测（完整的PPO算法实现）"""
        # 导入必要的环境类
        from flight_recovery_env import FlightRecoveryEnv

        total_results = []
        n_flight = len(flights_df)
        flight_ptr = 0
        batch_id = 0

        while flight_ptr < n_flight:
            batch_df = flights_df.iloc[flight_ptr:flight_ptr+batch_flight_size]
            batch_df, flight_real_mask = self._pad_flights_to_batch(batch_df, batch_flight_size, slot_count)

            # 创建环境
            env = FlightRecoveryEnv(
                batch_df, slots, available_slots, slot_length=slot_length, max_per_slot=max_per_slot,
                global_slot_key2idx=global_slot_key2idx,
                global_slot_counts=global_slot_counts,
                allow_cancel=allow_cancel,
                use_expected_depature=use_expected_departure
            )

            state = env.reset()
            done = False
            n_slot = len(slots)

            step_count = 0
            while not done and step_count < 1000:  # 防止无限循环
                mask = self._get_action_mask_with_virtuals(env, flight_real_mask, slot_real_mask)
                mask_flat = mask.flatten()
                if not mask_flat.any():
                    print(f"No valid actions available, breaking at step {step_count}")
                    break

                with torch.no_grad():
                    action_idx, log_prob, value, _ = agent.get_action(state, mask, device=self.device, temperature=1.0)

                flight_idx, slot_idx = self._decode_action(action_idx, batch_flight_size, n_slot)

                if not mask_flat[action_idx]:
                    possible_idxs = np.where(mask_flat)[0]
                    if len(possible_idxs) > 0:
                        action_idx = np.random.choice(possible_idxs)
                        flight_idx, slot_idx = self._decode_action(action_idx, batch_flight_size, n_slot)
                    else:
                        print(f"No valid backup actions, breaking at step {step_count}")
                        break

                next_state, reward, done = env.step((flight_idx, slot_idx))
                state = next_state
                step_count += 1

                if step_count % 10 == 0:
                    print(f"PPO step {step_count}, reward: {reward:.2f}, done: {done}")

            assign_df = env.get_assignment_results()
            assign_df['batch_id'] = batch_id
            assign_df = assign_df.iloc[:sum(flight_real_mask)]
            total_results.append(assign_df)
            flight_ptr += batch_flight_size
            batch_id += 1

        if total_results:
            final_assign_df = pd.concat(total_results, ignore_index=True)

            # 处理缺失的航班
            missing_flights = set(flights_df['flight_id']) - set(final_assign_df['flight_id'])
            if missing_flights:
                missing_rows = []
                for fid in missing_flights:
                    flight_row = flights_df[flights_df['flight_id'] == fid].iloc[0]
                    missing_rows.append({
                        'flight_id': fid,
                        'scheduled_departure': flight_row['scheduled_departure'],
                        'actual_departure': flight_row['actual_departure'] if 'actual_departure' in flight_row else pd.NaT,
                        'expected_departure': flight_row.get('expected_departure', flight_row.get('actual_departure', pd.NaT)),
                        'passenger_count': flight_row['passenger_count'],
                        'new_scheduled_departure': None,
                        'cancelled': True,
                        'batch_id': -1
                    })
                final_assign_df = pd.concat([final_assign_df, pd.DataFrame(missing_rows)], ignore_index=True)
        else:
            final_assign_df = pd.DataFrame()

        return final_assign_df, pd.DataFrame()

    def _merge_flights(self, df):
        """合并航班功能"""
        # 简化的合并逻辑，实际可以根据需要扩展
        return df

    def _convert_to_display_format(self, assignment_df):
        """转换为显示格式"""
        if assignment_df.empty:
            return pd.DataFrame()

        result_data = []
        for _, row in assignment_df.iterrows():
            result_data.append({
                'flight_id': row['flight_id'],
                'scheduled_departure': row['scheduled_departure'],
                'actual_departure': row.get('actual_departure', ''),
                'expected_departure': row.get('expected_departure', ''),
                'optimized_departure': row.get('new_scheduled_departure', row.get('scheduled_departure', '')),
                'delay_reduction': 0  # 可以根据实际情况计算
            })

        return pd.DataFrame(result_data)


# ============= 神经网络模型（从predict.py移植）=============
class MaskedActorCritic(nn.Module):
    def __init__(self, state_dim, action_dim, hidden_dim=128):
        super(MaskedActorCritic, self).__init__()
        self.fc1 = nn.Linear(state_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.actor_head = nn.Linear(hidden_dim, action_dim)
        self.critic_head = nn.Linear(hidden_dim, 1)

    def forward(self, x):
        x = torch.relu(self.fc1(x))
        x = torch.relu(self.fc2(x))
        logits = self.actor_head(x)
        value = self.critic_head(x)
        return logits, value

    def get_action(self, state, action_mask, device='cpu', temperature=2.0):
        """获取动作（从predict.py移植）"""
        def flatten_state(state):
            return np.array(state[0] + state[1], dtype=np.float32)

        state_tensor = torch.tensor(flatten_state(state), dtype=torch.float32).unsqueeze(0).to(device)
        logits, value = self.forward(state_tensor)
        mask_tensor = torch.tensor(action_mask.flatten(), dtype=torch.bool).to(device)
        logits = logits.masked_fill(~mask_tensor, float('-inf'))
        probs = torch.softmax(logits / temperature, dim=-1)
        dist = torch.distributions.Categorical(probs)
        action_idx = dist.sample().item()
        log_prob = dist.log_prob(torch.tensor(action_idx).to(device)).item()
        return action_idx, log_prob, value.item(), probs.cpu().detach().numpy()
