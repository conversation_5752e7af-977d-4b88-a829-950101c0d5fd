#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测模型模块
实现流量预测、运行状态推演、停止起降情景推演等核心算法
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random
import os
import warnings
from typing import Dict, List, Tuple, Optional

# 深度学习相关导入
try:
    import torch
    import torch.nn as nn
    from torch.utils.data import Dataset, DataLoader
    from torchvision import models, transforms
    from transformers import BertTokenizer, BertModel
    from sklearn.preprocessing import StandardScaler
    from PIL import Image
    import math
    from tqdm import tqdm
    DEEP_LEARNING_AVAILABLE = True
except ImportError:
    DEEP_LEARNING_AVAILABLE = False
    print("深度学习依赖未安装，将使用简化版流量预测算法")

warnings.filterwarnings('ignore')


# 深度学习模型配置和类
if DEEP_LEARNING_AVAILABLE:
    class Config:
        """深度学习模型配置"""
        def __init__(self, base_path="."):
            self.BASE_PATH = base_path
            self.BERT_MODEL_PATH = os.path.join(base_path, 'model', 'bert_models')
            self.RESNET_WEIGHTS_PATH = os.path.join(base_path, 'model', 'resnet50-0676ba61.pth')
            self.MODEL_WEIGHTS_PATH = os.path.join(base_path, 'model', 'best_transformer_model.pth')

            self.HISTORICAL_DAYS = 7
            self.INPUT_SEQ_LEN = self.HISTORICAL_DAYS + 1
            self.IMG_SIZE = 224
            self.EMBED_DIM = 512
            self.NUM_TABULAR_FEATURES = 2 + 16

            # Transformer配置
            self.TRANSFORMER_NHEAD = 8
            self.TRANSFORMER_ENCODER_LAYERS = 5
            self.TRANSFORMER_DIM_FEEDFORWARD = 2048
            self.DROPOUT = 0.1
            self.BATCH_SIZE = 6

            self.DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    class PositionalEncoding(nn.Module):
        """位置编码"""
        def __init__(self, d_model, dropout=0.1, max_len=5000):
            super(PositionalEncoding, self).__init__()
            self.dropout = nn.Dropout(p=dropout)
            position = torch.arange(max_len).unsqueeze(1)
            div_term = torch.exp(torch.arange(0, d_model, 2) * (-math.log(10000.0) / d_model))
            pe = torch.zeros(max_len, 1, d_model)
            pe[:, 0, 0::2] = torch.sin(position * div_term)
            pe[:, 0, 1::2] = torch.cos(position * div_term)
            self.register_buffer('pe', pe)

        def forward(self, x):
            x = x + self.pe[:x.size(0)]
            return self.dropout(x)

    class FlightTrafficModel(nn.Module):
        """深度学习流量预测模型"""
        def __init__(self, cfg, bert_model):
            super(FlightTrafficModel, self).__init__()
            self.cfg = cfg

            # 图像特征提取器
            resnet = models.resnet50(weights=None)
            if os.path.exists(cfg.RESNET_WEIGHTS_PATH):
                resnet.load_state_dict(torch.load(cfg.RESNET_WEIGHTS_PATH, map_location=cfg.DEVICE))
            self.image_feature_extractor = nn.Sequential(*list(resnet.children())[:-1])
            self.image_aggregator = nn.GRU(input_size=2048, hidden_size=cfg.EMBED_DIM, batch_first=True)

            # 文本特征提取器
            self.text_feature_extractor = bert_model
            self.text_aggregator = nn.GRU(input_size=bert_model.config.hidden_size, hidden_size=cfg.EMBED_DIM, batch_first=True)

            # 表格数据投影器
            self.tabular_projector = nn.Linear(cfg.NUM_TABULAR_FEATURES, cfg.EMBED_DIM)

            # 融合层
            self.fusion_projector = nn.Linear(cfg.EMBED_DIM * 3, cfg.EMBED_DIM)

            # Transformer编码器
            self.pos_encoder = PositionalEncoding(cfg.EMBED_DIM, cfg.DROPOUT)
            encoder_layer = nn.TransformerEncoderLayer(
                d_model=cfg.EMBED_DIM, nhead=cfg.TRANSFORMER_NHEAD,
                dim_feedforward=cfg.TRANSFORMER_DIM_FEEDFORWARD,
                dropout=cfg.DROPOUT, batch_first=True
            )
            self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=cfg.TRANSFORMER_ENCODER_LAYERS)

            # 预测头
            self.prediction_head = nn.Sequential(
                nn.LayerNorm(cfg.EMBED_DIM),
                nn.Linear(cfg.EMBED_DIM, cfg.EMBED_DIM // 2),
                nn.ReLU(),
                nn.Dropout(cfg.DROPOUT),
                nn.Linear(cfg.EMBED_DIM // 2, 2)
            )

        def forward(self, batch):
            B, S, *_ = batch['tabular_input'].shape

            fused_features_seq = []
            for t in range(S):
                # 图像特征
                images_t = batch['image_input'][:, t, :, :, :, :]
                N_imgs = images_t.shape[1]
                img_flat = images_t.reshape(B * N_imgs, *images_t.shape[2:])
                img_feats = self.image_feature_extractor(img_flat).view(B, N_imgs, -1)
                _, img_embedding = self.image_aggregator(img_feats)
                img_embedding = img_embedding.squeeze(0)

                # 文本特征
                text_ids_t = batch['text_input_ids'][:, t, :, :]
                text_mask_t = batch['text_attention_mask'][:, t, :, :]
                N_texts = text_ids_t.shape[1]
                text_ids_flat = text_ids_t.reshape(B * N_texts, -1)
                text_mask_flat = text_mask_t.reshape(B * N_texts, -1)
                text_outputs = self.text_feature_extractor(input_ids=text_ids_flat, attention_mask=text_mask_flat)
                text_cls_feats = text_outputs.last_hidden_state[:, 0, :].view(B, N_texts, -1)
                _, text_embedding = self.text_aggregator(text_cls_feats)
                text_embedding = text_embedding.squeeze(0)

                # 表格特征
                tabular_input_t = batch['tabular_input'][:, t, :]
                tabular_embedding = self.tabular_projector(tabular_input_t)

                # 融合特征
                fused_t = torch.cat([img_embedding, text_embedding, tabular_embedding], dim=1)
                fused_features_seq.append(self.fusion_projector(fused_t))

            # Transformer编码
            encoder_input = torch.stack(fused_features_seq, dim=1)
            encoder_input = self.pos_encoder(encoder_input.permute(1, 0, 2)).permute(1, 0, 2)
            encoder_output = self.transformer_encoder(encoder_input)

            # 预测
            final_vector = encoder_output[:, -1, :]
            prediction = self.prediction_head(final_vector)

            return prediction

    class FlightDataset(Dataset):
        """深度学习数据集类"""
        def __init__(self, cfg, tokenizer, df, image_base_path=""):
            self.cfg = cfg
            self.df = df.copy()
            self.tokenizer = tokenizer
            self.image_base_path = image_base_path

            # 确保时间列为datetime类型
            if 'time' in self.df.columns:
                self.df['time'] = pd.to_datetime(self.df['time'])
                self.df.set_index('time', inplace=True)

            # 定义特征列
            self.tabular_cols = ['计划离港数', '计划到港数'] + [f'方向_{d}' for d in
                                                                ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S',
                                                                 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW']]
            self.target_cols = ['实际离港数', '实际到港数']

            # 标准化数值特征
            self.tabular_scaler = StandardScaler()
            self.df[self.tabular_cols] = self.tabular_scaler.fit_transform(self.df[self.tabular_cols])

            self.target_scaler = StandardScaler()
            self.df[self.target_cols] = self.target_scaler.fit_transform(self.df[self.target_cols])

            # 图像变换
            self.image_transform = transforms.Compose([
                transforms.Resize((cfg.IMG_SIZE, cfg.IMG_SIZE)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ])

            self.index_to_time = self.df.index

        def __len__(self):
            return len(self.df)

        def __getitem__(self, idx):
            # 目标索引直接是传入的idx
            target_time = self.index_to_time[idx]

            # 获取输入特征序列
            input_timestamps = [target_time - pd.Timedelta(days=d) for d in range(self.cfg.HISTORICAL_DAYS, -1, -1)]

            all_tabular_data, all_images, all_texts = [], [], []

            for ts in input_timestamps:
                if ts in self.df.index:
                    row = self.df.loc[ts]
                else:
                    row = pd.Series(0, index=self.df.columns)

                # 数值特征
                all_tabular_data.append(torch.tensor(row[self.tabular_cols].values.astype(np.float32), dtype=torch.float32))

                # 图像特征
                images_for_ts = []
                for i in range(1, 13):
                    img_col = f'雷达图{i}'
                    if img_col in row.index and pd.notna(row[img_col]):
                        img_path = os.path.join(self.image_base_path, str(row[img_col]))
                        if os.path.exists(img_path):
                            try:
                                img = Image.open(img_path).convert('RGB')
                                images_for_ts.append(self.image_transform(img))
                            except:
                                images_for_ts.append(torch.zeros((3, self.cfg.IMG_SIZE, self.cfg.IMG_SIZE)))
                        else:
                            images_for_ts.append(torch.zeros((3, self.cfg.IMG_SIZE, self.cfg.IMG_SIZE)))
                    else:
                        images_for_ts.append(torch.zeros((3, self.cfg.IMG_SIZE, self.cfg.IMG_SIZE)))
                all_images.append(torch.stack(images_for_ts))

                # 文本特征
                texts_for_ts = []
                for i in range(1, 5):
                    texts_for_ts.append(str(row.get(f'天气报文{i}', '')))
                for i in range(1, 5):
                    texts_for_ts.append(str(row.get(f'天气预报{i}', '')))
                for i in range(1, 13):
                    texts_for_ts.append(str(row.get(f'雷达报告{i}', '')))
                all_texts.extend(texts_for_ts)

            # 堆叠特征
            tabular_data_seq = torch.stack(all_tabular_data)
            image_data_seq = torch.stack(all_images)

            # 文本编码
            text_tokens = self.tokenizer(all_texts, padding='max_length', truncation=True, max_length=64, return_tensors='pt')
            num_texts_per_step = 20
            text_ids_seq = text_tokens['input_ids'].view(self.cfg.INPUT_SEQ_LEN, num_texts_per_step, -1)
            text_mask_seq = text_tokens['attention_mask'].view(self.cfg.INPUT_SEQ_LEN, num_texts_per_step, -1)

            # 目标
            target_row = self.df.loc[target_time]
            target = torch.tensor(target_row[self.target_cols].values.astype(np.float32), dtype=torch.float32)

            return {
                'tabular_input': tabular_data_seq,
                'image_input': image_data_seq,
                'text_input_ids': text_ids_seq,
                'text_attention_mask': text_mask_seq,
                'target': target,
                'time_info': str(target_time)
            }


class FlightTrafficPredictor:
    """航班流量预测器 - 支持深度学习和传统算法"""

    def __init__(self, use_deep_learning=True, progress_callback=None):
        self.historical_data = None
        self.weather_data = None
        self.use_deep_learning = use_deep_learning and DEEP_LEARNING_AVAILABLE
        self.progress_callback = progress_callback  # 进度回调函数

        # 深度学习相关属性
        if self.use_deep_learning:
            self.config = None
            self.model = None
            self.tokenizer = None
            self.bert_model = None
            self.dataset = None
            self.tabular_scaler = None
            self.target_scaler = None
            self._is_model_loaded = False

            # 数据集缓存相关
            self._dataset_cache_key = None  # 用于检测数据是否变化
            self._cached_dataset = None     # 缓存的数据集

            # 预测结果缓存
            self._prediction_cache = {}     # 缓存预测结果

    def load_data(self, flight_file: str, weather_file: str = None):
        """
        加载航班数据

        Args:
            flight_file: 航班数据文件路径
            weather_file: 天气数据文件路径
        """
        if flight_file.endswith('.csv'):
            self.historical_data = pd.read_csv(flight_file)
        else:
            self.historical_data = pd.read_excel(flight_file)
        
        # 重置/失效缓存的数据集，因为数据源已更改
        self.dataset = None
        
        print(f"已加载航班数据: {len(self.historical_data)} 条记录")

        # 如果使用深度学习且数据格式符合要求，初始化深度学习模型
        if self.use_deep_learning and self._is_deep_learning_data_format():
            self._initialize_deep_learning_model()

    def get_available_dates(self) -> List[str]:
        """
        从加载的航班数据中提取所有唯一的日期

        Returns:
            List[str]: 包含所有可用日期的列表 (YYYY-MM-DD格式)
        """
        if self.historical_data is None:
            return []

        # 定义可能的时间字段名称
        possible_columns = ['time', '计划离港时间', '计划离港日期']
    
        time_column = None
        for col in possible_columns:
            if col in self.historical_data.columns:
                time_column = col
                break
    
        if time_column is None:
            return []

        try:
            dates = pd.to_datetime(self.historical_data[time_column]).dt.normalize().unique()
            return [d.strftime('%Y-%m-%d') for d in dates]
        except Exception as e:
            print(f"提取可用日期时出错: {e}")
            return []

    def _is_deep_learning_data_format(self) -> bool:
        """检查数据是否符合深度学习模型的格式要求"""
        if self.historical_data is None:
            return False

        required_columns = ['time', '计划离港数', '计划到港数', '实际离港数', '实际到港数']
        radar_columns = [f'雷达图{i}' for i in range(1, 13)]

        # 检查基本列是否存在
        has_basic_cols = all(col in self.historical_data.columns for col in required_columns)
        has_radar_cols = any(col in self.historical_data.columns for col in radar_columns)

        return has_basic_cols and has_radar_cols

    def _initialize_deep_learning_model(self):
        """初始化深度学习模型"""
        try:
            # 初始化配置
            self.config = Config()

            # 加载BERT模型和分词器
            if os.path.exists(self.config.BERT_MODEL_PATH):
                self.tokenizer = BertTokenizer.from_pretrained(self.config.BERT_MODEL_PATH)
                self.bert_model = BertModel.from_pretrained(self.config.BERT_MODEL_PATH)

                # 冻结BERT参数
                for param in self.bert_model.parameters():
                    param.requires_grad = False
                self.bert_model.to(self.config.DEVICE)

                # 初始化模型
                self.model = FlightTrafficModel(self.config, self.bert_model)

                # 加载训练好的权重
                if os.path.exists(self.config.MODEL_WEIGHTS_PATH):
                    self.model.load_state_dict(torch.load(self.config.MODEL_WEIGHTS_PATH, map_location=self.config.DEVICE))
                    self.model.to(self.config.DEVICE)
                    self.model.eval()
                    self._is_model_loaded = True
                    print("深度学习模型加载成功")
                else:
                    print(f"警告: 未找到模型权重文件 {self.config.MODEL_WEIGHTS_PATH}")
                    self.use_deep_learning = False
            else:
                print(f"警告: 未找到BERT模型 {self.config.BERT_MODEL_PATH}")
                self.use_deep_learning = False

        except Exception as e:
            print(f"深度学习模型初始化失败: {e}")
            self.use_deep_learning = False

    def predict_hourly_traffic(self, target_date: str, weather_data_path: str = None) -> pd.DataFrame:
        """
        基于航班计划数据预测指定日期的小时流量

        Args:
            target_date: 目标日期 (YYYY-MM-DD)
            weather_data_path: 天气数据路径（用于深度学习模型）

        Returns:
            DataFrame: 包含预测和实际流量对比的数据表
        """
        if self.historical_data is None:
            raise ValueError("请先加载航班数据")

        # 检查预测结果缓存
        cache_key = f"{target_date}_{weather_data_path or ''}"
        if cache_key in self._prediction_cache:
            if self.progress_callback:
                self.progress_callback(100)
            print("✅ 使用缓存的预测结果，瞬间完成")
            return self._prediction_cache[cache_key].copy()

        # 只使用深度学习模型
        if not self.use_deep_learning:
            raise ValueError("深度学习依赖不可用。请安装必要的依赖包：torch, transformers, torchvision, PIL, sklearn")

        if not self._is_model_loaded:
            raise ValueError("深度学习模型未正确加载。请检查模型文件是否存在：\n"
                           f"- BERT模型: {self.config.BERT_MODEL_PATH if self.config else 'model/bert_models'}\n"
                           f"- 模型权重: {self.config.MODEL_WEIGHTS_PATH if self.config else 'model/best_transformer_model.pth'}\n"
                           f"- ResNet权重: {self.config.RESNET_WEIGHTS_PATH if self.config else 'model/resnet50-0676ba61.pth'}")

        result = self._predict_with_deep_learning(target_date, weather_data_path)

        # 缓存预测结果
        self._prediction_cache[cache_key] = result.copy()

        return result

    def _get_or_create_dataset(self, weather_data_path: str = None) -> 'FlightDataset':
        """智能获取或创建数据集，支持缓存复用"""
        import hashlib

        # 生成数据集缓存键（基于数据内容和路径）
        data_hash = hashlib.md5(str(self.historical_data.shape).encode()).hexdigest()
        weather_path_hash = hashlib.md5(str(weather_data_path or "").encode()).hexdigest()
        cache_key = f"{data_hash}_{weather_path_hash}"

        # 检查是否可以复用缓存的数据集
        if (self._cached_dataset is not None and
            self._dataset_cache_key == cache_key):
            print("✓ 使用已缓存的数据集，预测速度更快")
            return self._cached_dataset

        # 创建新数据集并缓存
        print("⚡ 创建新数据集并缓存（首次或数据已更新）...")
        image_base_path = weather_data_path if weather_data_path else ""

        import time
        start_time = time.time()
        dataset = FlightDataset(self.config, self.tokenizer, self.historical_data, image_base_path)
        end_time = time.time()

        # 缓存数据集
        self._cached_dataset = dataset
        self._dataset_cache_key = cache_key

        print(f"✓ 数据集创建完成，耗时 {end_time - start_time:.2f} 秒")
        print(f"  - 数据集大小: {len(dataset)} 个样本")
        print(f"  - 历史天数: {self.config.HISTORICAL_DAYS} 天")
        print(f"  - 特征维度: {self.config.NUM_TABULAR_FEATURES}")

        return dataset

    def clear_dataset_cache(self):
        """清理数据集缓存，释放内存"""
        if self._cached_dataset is not None:
            print("🗑️ 清理数据集缓存，释放内存")
            self._cached_dataset = None
            self._dataset_cache_key = None

    def clear_prediction_cache(self):
        """清理预测结果缓存"""
        if self._prediction_cache:
            print("🗑️ 清理预测结果缓存，释放内存")
            self._prediction_cache.clear()

    def clear_all_cache(self):
        """清理所有缓存"""
        self.clear_dataset_cache()
        self.clear_prediction_cache()

    def get_cache_info(self) -> dict:
        """获取缓存信息"""
        return {
            'has_dataset_cache': self._cached_dataset is not None,
            'dataset_cache_key': self._dataset_cache_key,
            'dataset_size': len(self._cached_dataset) if self._cached_dataset else 0,
            'prediction_cache_size': len(self._prediction_cache),
            'cached_dates': list(self._prediction_cache.keys())
        }



    def _predict_with_deep_learning(self, target_date: str, weather_data_path: str = None) -> pd.DataFrame:
        """使用深度学习模型进行预测（优化版，支持智能缓存）"""
        try:
            if not self._is_model_loaded:
                raise ValueError("深度学习模型未正确加载，无法进行预测")

            # 1. 智能数据集缓存机制
            dataset = self._get_or_create_dataset(weather_data_path)

            # 2. 筛选目标日期的所有小时
            target_datetime = pd.to_datetime(target_date)
            start_time = target_datetime.replace(hour=7, minute=0, second=0)
            end_time = target_datetime.replace(hour=22, minute=59, second=59)
            
            time_col = pd.to_datetime(self.historical_data['time'])
            mask = (time_col >= start_time) & (time_col <= end_time)
            target_data_rows = self.historical_data[mask]

            if target_data_rows.empty:
                raise ValueError(f"未找到日期 {target_date} 的数据。")

            predictions = []

            # 3. 批量预测优化
            print(f"📊 开始预测 {len(target_data_rows)} 个时段...")

            # 收集所有样本索引
            sample_indices = list(target_data_rows.index)
            batch_size = min(self.config.BATCH_SIZE, len(sample_indices))
            total_batches = (len(sample_indices) + batch_size - 1) // batch_size

            import time
            start_time = time.time()

            # 批量处理
            for batch_idx in range(0, len(sample_indices), batch_size):
                batch_indices = sample_indices[batch_idx:batch_idx + batch_size]
                batch_samples = []
                batch_rows = []

                # 更新进度
                current_batch = batch_idx // batch_size + 1
                progress = 30 + int((current_batch / total_batches) * 50)  # 30-80%
                if self.progress_callback:
                    self.progress_callback(progress)

                # 收集批次数据
                for abs_idx in batch_indices:
                    row = target_data_rows.loc[abs_idx]
                    batch_rows.append(row)
                    sample = dataset[abs_idx]
                    batch_samples.append(sample)

                # 构建批次张量
                if batch_samples:
                    batch_tensors = {}
                    for key in batch_samples[0].keys():
                        if key != 'time_info':
                            batch_tensors[key] = torch.stack([s[key] for s in batch_samples]).to(self.config.DEVICE)

                    # 批量预测
                    with torch.no_grad():
                        batch_output = self.model(batch_tensors)

                    # 处理批次结果
                    batch_pred_scaled = batch_output.cpu().numpy()
                    batch_pred_unscaled = dataset.target_scaler.inverse_transform(batch_pred_scaled)

                    # 添加预测结果
                    for j, (row, pred_unscaled) in enumerate(zip(batch_rows, batch_pred_unscaled)):
                        current_time = pd.to_datetime(row['time'])
                        pred_arrive = max(0, pred_unscaled[0])  # 第一个是到港数
                        pred_depart = max(0, pred_unscaled[1])  # 第二个是离港数

                        predictions.append({
                            '时段': current_time.strftime('%H:%M') + '-' + (current_time + pd.Timedelta(hours=1)).strftime('%H:%M'),
                            '预测进港量': int(pred_arrive),
                            '预测出港量': int(pred_depart),
                            '预测总流量': int(pred_arrive + pred_depart),
                            '实际进港量': int(row.get('实际到港数', 0)),
                            '实际出港量': int(row.get('实际离港数', 0)),
                            '实际总流量': int(row.get('实际到港数', 0) + row.get('实际离港数', 0)),
                            '小时偏差率': 0.0,
                            '备注': "正常"
                        })

            end_time = time.time()
            print(f"⚡ 预测完成，耗时 {end_time - start_time:.2f} 秒")

            if not predictions:
                raise ValueError(f"未能为日期 {target_date} 生成任何预测结果。")

            # 7. 后处理和返回结果
            result_df = pd.DataFrame(predictions)
            result_df['小时偏差率'] = result_df.apply(
                lambda r: abs(r['预测总流量'] - r['实际总流量']) / r['实际总流量'] if r['实际总流量'] > 0 else 0, axis=1
            )
            result_df.loc[result_df['小时偏差率'] > 0.15, '备注'] = '>15%'

            print(f"深度学习模型预测完成，共预测 {len(result_df)} 个时段")
            return result_df

        except Exception as e:
            import traceback
            traceback.print_exc()
            raise ValueError(f"深度学习预测失败: {e}")




    
    def get_traffic_summary(self, traffic_df: pd.DataFrame) -> Dict:
        """
        获取流量预测汇总统计

        Args:
            traffic_df: 流量预测结果DataFrame

        Returns:
            Dict: 汇总统计信息
        """
        summary = {
            'total_planned_flights': traffic_df['预测总流量'].sum(),
            'total_actual_flights': traffic_df['实际总流量'].sum(),
            'average_deviation_rate': traffic_df['小时偏差率'].mean(),
            'max_deviation_rate': traffic_df['小时偏差率'].max(),
            'over_15_percent_count': len(traffic_df[traffic_df['小时偏差率'] > 0.15]),
            'peak_hour_planned': traffic_df.loc[traffic_df['预测总流量'].idxmax(), '时段'],
            'peak_hour_actual': traffic_df.loc[traffic_df['实际总流量'].idxmax(), '时段'],
            'meets_criteria': (
                traffic_df['小时偏差率'].mean() <= 0.1 and
                len(traffic_df[traffic_df['小时偏差率'] > 0.15]) <= 2
            )
        }

        return summary


class FlightDelayPredictor:
    """航班延误预测器 - 基于深度学习的多模态预测"""

    def __init__(self):
        self.delay_threshold = 15  # 固定延误阈值15分钟
        self.historical_data = None
        self.weather_data_path = None  # 雷达图数据路径
        self.model = None
        self.device = None
        self._initialize_model()

    def _initialize_model(self):
        """初始化深度学习模型"""
        try:
            import torch
            import torch.nn as nn
            from torch.utils.data import Dataset, DataLoader
            from torchvision import transforms, models
            from torchvision.models import ResNet50_Weights
            from PIL import Image
            from sklearn.preprocessing import StandardScaler, LabelEncoder
            import os

            # 禁用flash attention警告
            torch.backends.cuda.enable_flash_sdp(False)

            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            print(f"延误预测器使用设备: {self.device}")

            # 导入模型类（从delaypreREGtotal.py复制）
            self._define_model_classes()

            # 初始化模型
            self.model = self.FlightDelayModel()

            # 加载预训练权重
            model_path = os.path.join(os.path.dirname(__file__), '..', 'model', 'best_modelREG.pth')
            if os.path.exists(model_path):
                self.model.load_state_dict(torch.load(model_path, map_location=self.device, weights_only=True))
                print("成功加载预训练模型权重")
            else:
                print(f"警告: 未找到模型文件 {model_path}")

            self.model.to(self.device)
            self.model.eval()

        except Exception as e:
            print(f"模型初始化失败: {e}")
            self.model = None

    def _define_model_classes(self):
        """定义模型类（从delaypreREGtotal.py复制）"""
        import torch
        import torch.nn as nn
        from torchvision import models
        from torchvision.models import ResNet50_Weights

        # 配置参数
        self.config = type('Config', (), {
            'cnn_feat_dim': 512,
            'text_feat_dim': 256,
            'transformer_d_model': 768,
            'transformer_nhead': 8,
            'transformer_hid_dim': 1024,
            'transformer_num_layers': 3,
            'num_radar_images': 6,
            'image_size': (448, 448),
            'batch_size': 4
        })()

        class SpatialAttention(nn.Module):
            """空间注意力模块"""
            def __init__(self, in_channels):
                super(SpatialAttention, self).__init__()
                self.conv1 = nn.Conv2d(in_channels, 1, kernel_size=7, padding=3, bias=False)
                self.sigmoid = nn.Sigmoid()

            def forward(self, x):
                attn = self.conv1(x)
                attn = self.sigmoid(attn)
                return x * attn

        class RadarFeatureExtractor(nn.Module):
            """雷达图像特征提取器: CNN + Spatial Attention"""
            def __init__(self, output_dim=512):
                super(RadarFeatureExtractor, self).__init__()
                # 直接使用本地权重文件
                local_resnet_path = os.path.join(os.path.dirname(__file__), '..', 'model', 'resnet50-0676ba61.pth')
                resnet = models.resnet50(weights=None)
                resnet.load_state_dict(torch.load(local_resnet_path, map_location='cpu', weights_only=True))
                print("使用本地ResNet50权重文件")

                self.backbone = nn.Sequential(*list(resnet.children())[:-2])
                self.attention = SpatialAttention(2048)
                self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
                self.fc = nn.Linear(2048, output_dim)
                self.dropout = nn.Dropout(0.1)
                weights = torch.tensor([6, 5, 4, 3, 2, 1], dtype=torch.float32)
                self.weights = nn.Parameter(weights / weights.sum(), requires_grad=False)

            def forward(self, x):
                B, N, C, H, W = x.shape
                x = x.view(B * N, C, H, W)
                x = self.backbone(x)
                x = self.attention(x)
                x = self.avgpool(x)
                x = x.view(x.size(0), -1)
                x = self.fc(x)
                x = self.dropout(x)
                x = x.view(B, N, -1)
                x = (x * self.weights.view(1, -1, 1)).sum(dim=1)
                return x

        class TextFeatureExtractor(nn.Module):
            """文本/数值特征提取器 - 匹配原始训练配置"""
            def __init__(self, num_numeric=3, num_categorical=2, cat_vocab_sizes=[10, 1000], embed_dim=64, output_dim=256):
                super(TextFeatureExtractor, self).__init__()
                # 类别特征嵌入层
                self.cat_embeddings = nn.ModuleList()
                for vocab_size in cat_vocab_sizes:
                    self.cat_embeddings.append(nn.Embedding(vocab_size, embed_dim))

                # 数值特征处理
                self.num_fc = nn.Linear(num_numeric, num_numeric * embed_dim)

                # 融合层
                total_input_dim = num_categorical * embed_dim + num_numeric * embed_dim
                self.fc1 = nn.Linear(total_input_dim, 512)
                self.fc2 = nn.Linear(512, output_dim)
                self.dropout = nn.Dropout(0.1)
                self.relu = nn.ReLU()

            def forward(self, x):
                # x: [B, 13] = [B, 8个数值特征 + 5个类别特征]
                # 但只使用前3个数值特征和前2个类别特征（匹配原始训练）
                numeric_data = x[:, :3]  # 前3列是数值特征（从8个中取前3个）
                categorical_data = x[:, 8:10].long()  # 第8-9列是类别特征（从5个中取前2个）

                # 处理数值特征
                num_feats = self.num_fc(numeric_data)
                num_feats = self.relu(num_feats)
                num_feats = num_feats.view(num_feats.size(0), -1)  # 展平数值特征确保二维

                # 处理类别特征
                cat_feats = []
                for i, embed in enumerate(self.cat_embeddings):
                    # 确保类别索引在有效范围内
                    cat_idx = categorical_data[:, i].clamp(0, embed.num_embeddings - 1)
                    feat = embed(cat_idx)
                    feat = feat.view(feat.size(0), -1)  # 展平类别特征确保二维
                    cat_feats.append(feat)
                cat_feats = torch.cat(cat_feats, dim=1)

                # 融合特征
                combined = torch.cat([num_feats, cat_feats], dim=1)
                x = self.fc1(combined)
                x = self.relu(x)
                x = self.dropout(x)
                x = self.fc2(x)
                return x

        class TransformerPredictor(nn.Module):
            """基于Transformer的延误预测模块"""
            def __init__(self, cnn_dim=512, text_dim=256, d_model=768, nhead=8, hid_dim=1024, num_layers=3):
                super(TransformerPredictor, self).__init__()
                self.fusion = nn.Linear(768, d_model)
                self.pos_encoder = nn.Parameter(torch.randn(1, d_model))
                encoder_layers = nn.TransformerEncoderLayer(d_model, nhead, hid_dim, dropout=0.1, batch_first=True)
                self.transformer_encoder = nn.TransformerEncoder(encoder_layers, num_layers)
                self.predictor = nn.Sequential(
                    nn.Linear(d_model, 256),
                    nn.ReLU(),
                    nn.Dropout(0.1),
                    nn.Linear(256, 1)
                )

            def forward(self, cnn_feats, text_feats):
                cnn_feats = cnn_feats.view(cnn_feats.size(0), -1)
                text_feats = text_feats.view(text_feats.size(0), -1)
                combined = torch.cat([cnn_feats, text_feats], dim=1)
                assert combined.shape[1] == 768, f"融合特征维度不匹配: 实际{combined.shape[1]}, 预期768"
                x = self.fusion(combined)
                x = x.unsqueeze(1) + self.pos_encoder.unsqueeze(0)
                x = self.transformer_encoder(x)
                x = x.squeeze(1)
                delay_pred = self.predictor(x)
                return delay_pred.squeeze(1)

        class FlightDelayModel(nn.Module):
            """完整的航班延误预测模型"""
            def __init__(self):
                super(FlightDelayModel, self).__init__()
                self.radar_extractor = RadarFeatureExtractor()
                self.text_extractor = TextFeatureExtractor()
                self.predictor = TransformerPredictor()

            def forward(self, radar_images, text_features):
                radar_feats = self.radar_extractor(radar_images)
                text_feats = self.text_extractor(text_features)
                delay_pred = self.predictor(radar_feats, text_feats)
                return delay_pred

        # 将类绑定到self
        self.SpatialAttention = SpatialAttention
        self.RadarFeatureExtractor = RadarFeatureExtractor
        self.TextFeatureExtractor = TextFeatureExtractor
        self.TransformerPredictor = TransformerPredictor
        self.FlightDelayModel = FlightDelayModel

    def set_weather_data_path(self, weather_path: str):
        """
        设置天气数据路径（雷达图文件夹）

        Args:
            weather_path: 雷达图文件夹路径
        """
        self.weather_data_path = weather_path
        print(f"延误预测器已设置雷达图路径: {weather_path}")

    def load_data(self, flight_file: str):
        """
        加载航班数据并进行预处理

        Args:
            flight_file: 航班数据文件路径
        """
        # 加载原始数据
        if flight_file.endswith('.csv'):
            raw_data = pd.read_csv(flight_file)
        else:
            raw_data = pd.read_excel(flight_file)
        print(f"延误预测器已加载原始航班数据: {len(raw_data)} 条记录")

        # 进行数据预处理
        print("正在进行数据预处理...")
        self.historical_data = self._process_flight_data(raw_data)
        print(f"数据预处理完成，处理后数据: {len(self.historical_data)} 条记录")

    def _process_flight_data(self, df):
        """
        对原始航班数据进行预处理，转换为延误预测算法需要的格式
        集成了process_flight_data.py中的处理逻辑
        """
        try:
            # 步骤1: 筛选航班
            df = self._filter_flights_df(df)

            # 步骤2: 提取时间特征
            df = self._extract_time_features_df(df)

            # 步骤3: 添加拥堵特征
            df = self._add_departure_traffic_df(df)

            # 步骤4: 计算延误特征
            df = self._calculate_delay_df(df)

            # 步骤5: 筛选ZGGG航班
            df = self._filter_zggg_flights_df(df)

            # 步骤6: 匹配雷达图（如果有雷达图路径）
            if self.weather_data_path:
                df = self._match_radar_images_df(df)

            # 步骤7: 添加时间分钟数特征
            df = self._add_time_minute_df(df)

            # 步骤8: 剔除雷达图_1字段为空的数据
            df = self._filter_radar_data_df(df)

            return df

        except Exception as e:
            print(f"数据预处理过程中发生错误: {e}")
            import traceback
            print(traceback.format_exc())
            return df

    def _filter_flights_df(self, df):
        """筛选航班数据：剔除非客运类别、航班号以Z结尾、机尾号为空或为'-'的航班"""
        original_rows = len(df)
        print(f"原始数据行数: {original_rows}")

        # 剔除非客运类别的机型（性质字段不为G或J）
        if '性质' in df.columns:
            df = df[df['性质'].isin(['G', 'J'])]

        # 剔除航班号字段最后一位为Z的航班
        if '航班号' in df.columns:
            df = df[~df['航班号'].str.endswith('Z')]

        # 剔除机尾号字段为空的航班
        if '机尾号' in df.columns:
            df = df.dropna(subset=['机尾号'])
            df = df[df['机尾号'] != '-']

        filtered_rows = len(df)
        print(f"步骤1 (filter_flights) 完成: 筛选后的数据行数: {filtered_rows} (已剔除 {original_rows - filtered_rows} 条记录)")
        return df

    def _extract_time_features_df(self, df):
        """为DataFrame添加时间特征"""
        if '计划离港时间' in df.columns:
            df['计划离港时间'] = pd.to_datetime(df['计划离港时间'])
            df['计划离港日期'] = df['计划离港时间'].dt.strftime('%Y-%m-%d')
            df['计划离港时刻(24小时制)'] = df['计划离港时间'].dt.hour
            df['当天星期'] = df['计划离港时间'].dt.weekday + 1
            df['所处月份'] = df['计划离港时间'].dt.month

            # 简化的日期类型判断（避免依赖chinese_calendar）
            df['日期类型'] = df['当天星期'].apply(lambda x: '普通周末' if x >= 6 else '普通工作日')

        print("步骤2 (extract_time_features) 完成: 已添加时间特征。")
        return df

    def _add_departure_traffic_df(self, df):
        """为数据集添加起飞机场在[T-6h, T-3h)窗口的拥堵特征"""
        print("正在计算拥堵特征...")

        if '计划起飞站四字码' not in df.columns or '计划到达站四字码' not in df.columns:
            print("警告: 缺少机场代码字段，跳过拥堵特征计算")
            df['离港前6-3h离港数'] = 0
            df['离港前6-3h到港数'] = 0
            return df

        DEP_AIRPORT_COL = '计划起飞站四字码'
        ARR_AIRPORT_COL = '计划到达站四字码'
        DEP_TIME_COL = '计划离港时间'
        ARR_TIME_COL = '计划到港时间'
        OUTPUT_DEPARTURES_COL = '离港前6-3h离港数'
        OUTPUT_ARRIVALS_COL = '离港前6-3h到港数'

        df[DEP_TIME_COL] = pd.to_datetime(df[DEP_TIME_COL])
        if ARR_TIME_COL in df.columns:
            df[ARR_TIME_COL] = pd.to_datetime(df[ARR_TIME_COL])

        # 按机场分组预处理时间序列
        departures_by_airport = {
            airport: group[DEP_TIME_COL].sort_values()
            for airport, group in df.groupby(DEP_AIRPORT_COL)
        }

        arrivals_by_airport = {}
        if ARR_TIME_COL in df.columns:
            arrivals_by_airport = {
                airport: group[ARR_TIME_COL].sort_values()
                for airport, group in df.groupby(ARR_AIRPORT_COL)
            }

        def calculate_congestion_for_row(flight_row):
            airport = flight_row[DEP_AIRPORT_COL]
            dep_time = flight_row[DEP_TIME_COL]
            start_window = dep_time - pd.Timedelta(hours=6)
            end_window = dep_time - pd.Timedelta(hours=3)

            # 计算离港数
            dep_count = 0
            if airport in departures_by_airport:
                dep_ts = departures_by_airport[airport]
                start_idx = dep_ts.searchsorted(start_window, side='left')
                end_idx = dep_ts.searchsorted(end_window, side='left')
                dep_count = end_idx - start_idx

            # 计算到港数
            arr_count = 0
            if airport in arrivals_by_airport:
                arr_ts = arrivals_by_airport[airport]
                start_idx = arr_ts.searchsorted(start_window, side='left')
                end_idx = arr_ts.searchsorted(end_window, side='left')
                arr_count = end_idx - start_idx

            return pd.Series([dep_count, arr_count], index=[OUTPUT_DEPARTURES_COL, OUTPUT_ARRIVALS_COL])

        congestion_features = df.apply(calculate_congestion_for_row, axis=1)
        df = pd.concat([df, congestion_features], axis=1)
        print("步骤3 (add_departure_traffic) 完成: 已添加拥堵特征。")
        return df

    def _calculate_delay_df(self, df):
        """计算航班延误特征"""
        PLAN_DEP_TIME_COL = '计划离港时间'
        PLAN_ARR_TIME_COL = '计划到港时间'
        ACTUAL_DEP_TIME_COL = '实际离港时间'
        ACTUAL_ARR_TIME_COL = '实际到港时间'
        DEP_DELAY_COL = '离港延误'
        ARR_DELAY_COL = '到港延误'
        DEP_DELAY_CAT_COL = '离港延误（分类）'
        ARR_DELAY_CAT_COL = '到港延误（分类）'
        GRACE_PERIOD = 15

        # 确保时间列是datetime类型
        for col in [PLAN_DEP_TIME_COL, PLAN_ARR_TIME_COL, ACTUAL_DEP_TIME_COL, ACTUAL_ARR_TIME_COL]:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')

        # 计算延误（分钟）
        if ACTUAL_DEP_TIME_COL in df.columns:
            df[DEP_DELAY_COL] = ((df[ACTUAL_DEP_TIME_COL] - df[PLAN_DEP_TIME_COL]).dt.total_seconds() / 60 - GRACE_PERIOD).round().astype('Int64')
            df[DEP_DELAY_COL] = df[DEP_DELAY_COL].fillna(0)
            df[DEP_DELAY_CAT_COL] = df[DEP_DELAY_COL].apply(self._classify_delay).astype('Int64')

        if ACTUAL_ARR_TIME_COL in df.columns and PLAN_ARR_TIME_COL in df.columns:
            df[ARR_DELAY_COL] = ((df[ACTUAL_ARR_TIME_COL] - df[PLAN_ARR_TIME_COL]).dt.total_seconds() / 60 - GRACE_PERIOD).round().astype('Int64')
            df[ARR_DELAY_COL] = df[ARR_DELAY_COL].fillna(0)
            df[ARR_DELAY_CAT_COL] = df[ARR_DELAY_COL].apply(self._classify_delay).astype('Int64')

        print("步骤4 (calculate_delay) 完成: 已计算延误特征。")
        return df

    def _classify_delay(self, delay):
        """根据延误分钟数进行分类"""
        if pd.isna(delay):
            return None
        if delay > 0:
            return np.ceil(delay / 5.0)
        elif delay > -16:
            return 0
        else:
            return np.floor((delay + 15) / 15.0)

    def _filter_zggg_flights_df(self, df):
        """筛选出计划起飞站为ZGGG的航班数据"""
        original_rows = len(df)
        departure_field = "计划起飞站四字码"

        if departure_field in df.columns:
            filtered_df = df[df[departure_field] == 'ZGGG'].copy()
        else:
            print("警告: 未找到起飞站字段，保留所有数据")
            filtered_df = df.copy()

        print(f"步骤5 (filter_zggg_flights) 完成: 筛选ZGGG离港航班，保留 {len(filtered_df)} 条记录 (原为 {original_rows} 条)。")
        return filtered_df

    def _match_radar_images_df(self, df):
        """为每个航班匹配雷达图并添加到DataFrame中"""
        import os
        import re
        import math

        # 雷达图匹配参数
        RADAR_INDEX_DIFF = 6
        RADAR_START_HOURS_BEFORE = 3
        RADAR_END_HOURS_BEFORE = 6
        RADAR_PICTURE_NUM = 7

        # 初始化雷达图列
        for i in range(RADAR_PICTURE_NUM):
            df[f'雷达图_{i+1}'] = None

        if not self.weather_data_path or not os.path.exists(self.weather_data_path):
            print("警告: 雷达图路径不存在，跳过雷达图匹配")
            return df

        error_count = 0

        for index, row in df.iterrows():
            try:
                departure_datetime = pd.to_datetime(row["计划离港时间"])
                departure_date = departure_datetime.strftime('%Y-%m-%d')
                departure_time = departure_datetime.strftime('%H:%M')

                radar_images, error = self._get_radar_images_for_flight(
                    departure_date, departure_time, self.weather_data_path,
                    RADAR_INDEX_DIFF, RADAR_START_HOURS_BEFORE, RADAR_END_HOURS_BEFORE
                )

                if error:
                    print(f"行 {index+1}: {error}")
                    error_count += 1
                    continue

                for i, image_path in enumerate(radar_images[:RADAR_PICTURE_NUM]):
                    df.at[index, f'雷达图_{i+1}'] = image_path

            except Exception as e:
                print(f"行 {index+1}: 处理错误 - {e}")
                error_count += 1

        print(f"步骤6 (match_radar_images) 完成: 已匹配雷达图。错误数: {error_count}")
        return df

    def _get_radar_images_for_flight(self, departure_date, departure_time, radar_base_path, index_diff, start_hours_before, end_hours_before):
        """根据起飞日期和时间获取符合条件的雷达图"""
        import os
        import re

        try:
            day = int(departure_date.split('-')[2])
            day_folder = f"{day:02d}"
            day_path = os.path.join(radar_base_path, day_folder)
            if not os.path.exists(day_path):
                return [], f"日期 {day_folder} 的雷达图文件夹不存在"

            radar_images = sorted([file[:4] for file in os.listdir(day_path) if file.endswith('.png') and re.match(r'\d{4}\.png', file)])

            if not radar_images:
                return [], f"在 {day_folder} 文件夹中未找到雷达图"

            dep_hour, dep_minute = map(int, departure_time.split(':'))
            dep_total_minutes = dep_hour * 60 + dep_minute

            target_start_minutes = dep_total_minutes - start_hours_before * 60

            closest_image = None
            closest_time_diff = float('inf')

            for radar_time in radar_images:
                radar_hour = int(radar_time[:2])
                radar_minute = int(radar_time[2:])
                radar_total_minutes = radar_hour * 60 + radar_minute
                time_diff = target_start_minutes - radar_total_minutes
                if 0 <= time_diff < closest_time_diff:
                    closest_time_diff = time_diff
                    closest_image = radar_time

            if closest_image is None:
                return [], f"没有找到早于离港前{start_hours_before}小时的雷达图"

            closest_index = radar_images.index(closest_image)
            selected_images = [f"{day_folder}/{closest_image}.png"]

            current_index = closest_index
            target_end_minutes = dep_total_minutes - end_hours_before * 60

            while current_index >= index_diff:
                current_index -= index_diff
                radar_time = radar_images[current_index]
                radar_hour = int(radar_time[:2])
                radar_minute = int(radar_time[2:])
                radar_total_minutes = radar_hour * 60 + radar_minute
                if radar_total_minutes < target_end_minutes:
                    break
                selected_images.append(f"{day_folder}/{radar_time}.png")

            return selected_images, None
        except Exception as e:
            return [], str(e)

    def _add_time_minute_df(self, df):
        """为DataFrame增加时间的分钟数和分类特征"""
        import math

        def time_to_minutes(time_val):
            if pd.isnull(time_val):
                return None
            try:
                time_obj = pd.to_datetime(time_val).time()
                return time_obj.hour * 60 + time_obj.minute
            except (ValueError, TypeError):
                return None

        def categorize_minutes(minutes):
            if pd.isnull(minutes):
                return None
            return math.floor(minutes / 5) + 1

        if '实际离港时间' in df.columns:
            df['实际离港时间分钟数'] = df['实际离港时间'].apply(time_to_minutes)
            df['实际离港时间分钟数（分类）'] = df['实际离港时间分钟数'].apply(categorize_minutes)

        if '实际到港时间' in df.columns:
            df['实际到港时间分钟数'] = df['实际到港时间'].apply(time_to_minutes)
            df['实际到港时间分钟数（分类）'] = df['实际到港时间分钟数'].apply(categorize_minutes)

        print("步骤7 (add_time_minute) 完成: 已增加时间分钟数特征。")
        return df

    def _filter_radar_data_df(self, df):
        """剔除雷达图_1字段为空的数据"""
        original_rows = len(df)

        if '雷达图_1' in df.columns:
            # 剔除雷达图_1字段为空或None的数据
            df = df.dropna(subset=['雷达图_1'])
            df = df[df['雷达图_1'] != '']
            df = df[df['雷达图_1'].notna()]
        else:
            print("警告: 未找到雷达图_1字段，跳过雷达图数据筛选")

        filtered_rows = len(df)
        print(f"步骤8 (filter_radar_data) 完成: 剔除雷达图为空的数据，保留 {filtered_rows} 条记录 (原为 {original_rows} 条)。")
        return df

    def predict_flight_delays(self, target_date: str) -> pd.DataFrame:
        """
        预测指定日期每个航班的延误情况

        Args:
            target_date: 目标日期 (YYYY-MM-DD)

        Returns:
            DataFrame: 包含每个航班延误预测的数据表
        """
        if self.historical_data is None:
            raise ValueError("请先加载航班数据")

        # 筛选目标日期的出港航班数据
        # 注意：使用新数据格式的字段名
        date_column = '计划离港日期'
        if date_column not in self.historical_data.columns:
            # 兼容旧格式
            date_column = 'date'

        # 转换日期格式进行比较
        target_date_pd = pd.to_datetime(target_date).date()
        df_filtered = self.historical_data.copy()

        # 确保日期列是datetime类型
        df_filtered[date_column] = pd.to_datetime(df_filtered[date_column])
        df_filtered = df_filtered[df_filtered[date_column].dt.date == target_date_pd]

        # 移除时段筛选，与predict.py保持一致（处理全天24小时航班）
        if '计划离港时间' in df_filtered.columns:
            df_filtered['计划离港时间'] = pd.to_datetime(df_filtered['计划离港时间'])
            print(f"延误预测数据准备完成: {len(df_filtered)} 条航班记录（全天24小时）")

        # 只预测出港航班（从广州白云机场出发）
        if '计划起飞站四字码' in df_filtered.columns:
            df_filtered = df_filtered[df_filtered['计划起飞站四字码'] == 'ZGGG']
        elif 'planned_dep_airport' in df_filtered.columns:
            df_filtered = df_filtered[df_filtered['planned_dep_airport'] == 'ZGGG']

        if df_filtered.empty:
            raise ValueError(f"未找到日期 {target_date} 的出港航班数据")

        print(f"找到 {len(df_filtered)} 条航班数据进行延误预测")

        # 使用深度学习模型进行预测
        if self.model is not None:
            predictions = self._predict_with_deep_learning(df_filtered)
        else:
            raise RuntimeError("深度学习模型未正确加载，无法进行延误预测")

        return pd.DataFrame(predictions)

    def _predict_with_deep_learning(self, df: pd.DataFrame) -> list:
        """
        使用深度学习模型进行预测

        Args:
            df: 筛选后的航班数据

        Returns:
            list: 预测结果列表
        """
        import torch
        from torch.utils.data import Dataset, DataLoader
        from torchvision import transforms
        from PIL import Image
        from sklearn.preprocessing import StandardScaler, LabelEncoder
        import os

        # 创建数据集
        dataset = self._create_flight_dataset(df, self.weather_data_path)
        dataloader = DataLoader(dataset, batch_size=self.config.batch_size, shuffle=False, num_workers=0)

        predictions = []
        all_preds = []

        with torch.inference_mode():
            for batch in dataloader:
                radar_images = batch['radar_images'].to(self.device)
                text_features = batch['text_features'].to(self.device)

                outputs = self.model(radar_images, text_features)
                all_preds.extend(outputs.cpu().numpy())

        # 构建预测结果
        for i, (_, flight) in enumerate(df.iterrows()):
            # 模型预测的是减去15分钟阈值后的延误，这里需要加回来
            grace_period = 15
            predicted_delay_model_output = float(all_preds[i])
            actual_predicted_delay = predicted_delay_model_output + grace_period

            # 计算预测离港时间
            predicted_dep_time = self._calculate_predicted_time(
                flight['计划离港时间'], actual_predicted_delay
            )

            predictions.append({
                '航班号': flight['航班号'],
                '日期': flight['计划离港日期'].strftime('%Y-%m-%d') if hasattr(flight['计划离港日期'], 'strftime') else str(flight['计划离港日期']),
                '计划离港时间': flight['计划离港时间'],
                '实际离港时间': flight.get('实际离港时间', ''),
                '预测离港延误(分钟)': round(actual_predicted_delay-grace_period, 1),
                '预测离港时间': predicted_dep_time
            })

        return predictions

    def _create_flight_dataset(self, df: pd.DataFrame, weather_data_path: Optional[str] = None):
        """
        创建用于深度学习预测的数据集

        Args:
            df: 航班数据DataFrame
            weather_data_path: 雷达图所在的基准文件夹路径

        Returns:
            FlightDataset: 数据集对象
        """
        from torch.utils.data import Dataset
        from torchvision import transforms
        from PIL import Image
        from sklearn.preprocessing import StandardScaler, LabelEncoder
        import torch
        import os

        class FlightDataset(Dataset):
            def __init__(self, dataframe, image_base_path, image_size=(448, 448), transform=None):
                self.dataframe = dataframe.reset_index(drop=True)
                self.image_base_path = image_base_path or ""
                self.image_size = image_size
                self.transform = transform
                self.scaler = StandardScaler()
                self.label_encoders = {}

                # 定义特征列 - 完全匹配原始训练配置
                # 数值特征：7个（和原始文件完全一致）
                self.numeric_features = ['计划离港时刻(24小时制)', '当天星期', '所处月份', '计划地面航程_Mile',
                                       '离港前6-3h离港数', '离港前6-3h到港数', '计划轮挡时长(分钟)']
                # 日期特征：1个（会转换为数值）
                self.datetime_features = ['计划离港日期']
                # 类别特征：5个（和原始文件完全一致）
                self.categorical_features = ['日期类型', '航班号', '机型', '性质', '航线']

                # 处理缺失的计划轮挡时长字段
                if '计划轮挡时长(分钟)' not in dataframe.columns:
                    # 计算计划轮挡时长
                    if '计划到港时间' in dataframe.columns and '计划离港时间' in dataframe.columns:
                        try:
                            dep_time = pd.to_datetime(dataframe['计划离港时间'], format='%H:%M', errors='coerce')
                            arr_time = pd.to_datetime(dataframe['计划到港时间'], format='%H:%M', errors='coerce')
                            duration = (arr_time - dep_time).dt.total_seconds() / 60
                            self.dataframe['计划轮挡时长(分钟)'] = duration.fillna(120)  # 默认120分钟
                        except:
                            self.dataframe['计划轮挡时长(分钟)'] = 120  # 默认值
                    else:
                        self.dataframe['计划轮挡时长(分钟)'] = 120  # 默认值

                # 处理航线字段
                if '航线' not in dataframe.columns:
                    if '计划起飞站四字码' in dataframe.columns and '计划到达站四字码' in dataframe.columns:
                        self.dataframe['航线'] = dataframe['计划起飞站四字码'] + ' -> ' + dataframe['计划到达站四字码']
                    else:
                        self.dataframe['航线'] = 'UNKNOWN'

                # 处理datetime特征
                for col in self.datetime_features:
                    if col in self.dataframe.columns:
                        self.dataframe[col] = pd.to_datetime(self.dataframe[col]).astype('int64') // 10**9

                # 拟合标准化器和标签编码器
                all_numeric_features = self.numeric_features + self.datetime_features
                available_numeric = [col for col in all_numeric_features if col in self.dataframe.columns]
                if available_numeric:
                    self.scaler.fit(self.dataframe[available_numeric])

                for col in self.categorical_features:
                    if col in self.dataframe.columns:
                        le = LabelEncoder()
                        # 处理缺失值
                        values = self.dataframe[col].fillna('UNKNOWN').astype(str)
                        self.label_encoders[col] = le.fit(values)

            def __len__(self):
                return len(self.dataframe)

            def __getitem__(self, idx):
                row = self.dataframe.iloc[idx]

                # 加载雷达图像
                radar_images = []
                prev_valid_path = None
                relative_img_path = None

                for i in range(6):  # 6张雷达图
                    radar_col = f'雷达图_{i + 1}'
                    if radar_col in row and not pd.isna(row[radar_col]):
                        relative_img_path = str(row[radar_col])
                        prev_valid_path = relative_img_path
                    else:
                        relative_img_path = prev_valid_path
                    
                    img_path = os.path.join(self.image_base_path, relative_img_path) if self.image_base_path and relative_img_path else relative_img_path

                    try:
                        if img_path and os.path.exists(img_path):
                            image = Image.open(img_path).convert('RGB')
                            if self.transform:
                                image = self.transform(image)
                            radar_images.append(image)
                        else:
                            # 使用零矩阵替代缺失图像
                            radar_images.append(torch.zeros(3, self.image_size[0], self.image_size[1]))
                    except Exception as e:
                        print(f"加载图像失败 {img_path}: {e}")
                        radar_images.append(torch.zeros(3, self.image_size[0], self.image_size[1]))

                # 处理数值特征 - 完全匹配原始文件逻辑
                all_numeric_features = self.numeric_features + self.datetime_features
                # 使用DataFrame确保列名匹配（和原始文件一致）
                numerical_df = pd.DataFrame([row[all_numeric_features]], columns=all_numeric_features)
                numeric_data = self.scaler.transform(numerical_df)[0]

                # 处理类别特征 - 完全匹配原始文件逻辑
                categorical_data = []
                for col in self.categorical_features:
                    if col in row.index and col in self.label_encoders:
                        value = str(row[col]) if not pd.isna(row[col]) else 'UNKNOWN'
                        try:
                            encoded = self.label_encoders[col].transform([value])[0]
                        except:
                            encoded = 0  # 未知类别
                        categorical_data.append(encoded)
                    else:
                        categorical_data.append(0)

                # 合并特征：8个数值特征(7+1) + 5个类别特征 = 13个特征
                # 但模型只会使用前3个数值特征和前2个类别特征
                text_features = torch.tensor(np.concatenate([numeric_data, categorical_data]), dtype=torch.float32)

                return {
                    'radar_images': torch.stack(radar_images),
                    'text_features': text_features
                }

        # 创建图像变换
        transform = transforms.Compose([
            transforms.Resize(self.config.image_size),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

        return FlightDataset(df, weather_data_path, self.config.image_size, transform)



    def _calculate_predicted_time(self, planned_time, delay_minutes: float) -> str:
        """
        计算预测离港时间

        Args:
            planned_time: 计划离港时间 (可能是字符串或datetime对象)
            delay_minutes: 延误分钟数

        Returns:
            str: 预测离港时间 (HH:MM格式)
        """
        from datetime import datetime, timedelta
        import pandas as pd

        try:
            # 处理不同类型的时间输入
            if pd.isna(planned_time):
                return "00:00"

            if isinstance(planned_time, str):
                # 如果是字符串，尝试不同的格式
                if ':' in planned_time and len(planned_time.split(':')) >= 2:
                    time_part = planned_time.split(' ')[-1] if ' ' in planned_time else planned_time
                    planned_dt = datetime.strptime(time_part[:5], '%H:%M')
                else:
                    planned_dt = datetime.strptime(planned_time, '%H:%M')
            else:
                # 如果是datetime对象，直接使用
                if hasattr(planned_time, 'time'):
                    planned_dt = datetime.combine(datetime.today().date(), planned_time.time())
                else:
                    # 尝试转换为datetime
                    planned_dt = pd.to_datetime(planned_time)
                    if hasattr(planned_dt, 'time'):
                        planned_dt = datetime.combine(datetime.today().date(), planned_dt.time())

            # 加上延误时间
            predicted_dt = planned_dt + timedelta(minutes=int(delay_minutes))

            # 返回格式化的时间
            return predicted_dt.strftime('%H:%M')

        except Exception as e:
            print(f"时间计算错误: {e}, planned_time: {planned_time} (type: {type(planned_time)}), delay_minutes: {delay_minutes}")
            # 如果计算失败，尝试提取时间部分
            try:
                if hasattr(planned_time, 'strftime'):
                    return planned_time.strftime('%H:%M')
                else:
                    time_str = str(planned_time)
                    if ' ' in time_str:
                        time_part = time_str.split(' ')[-1]
                        return time_part[:5] if ':' in time_part else "00:00"
                    return time_str[:5] if ':' in time_str else "00:00"
            except:
                return "00:00"
