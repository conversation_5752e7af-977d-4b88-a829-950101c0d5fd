"""
推演模型模块
基于延误预测结果进行积压量分析
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional


class FlightSimulator:
    """航班推演器"""
    
    def __init__(self):
        self.delay_prediction_data = None
        self.simulation_results = None
    
    def load_delay_prediction_data(self, delay_df: pd.DataFrame):
        """
        加载延误预测数据
        
        Args:
            delay_df: 延误预测结果DataFrame，包含以下列：
                - 航班号
                - 日期
                - 计划离港时间
                - 实际离港时间
                - 预测离港延误(分钟)
                - 预测离港时间
        """
        self.delay_prediction_data = delay_df.copy()
        print(f"推演器已加载 {len(delay_df)} 条延误预测数据")
    
    def _parse_time_to_hour(self, time_obj) -> int:
        """
        将时间对象解析为小时数

        Args:
            time_obj: 时间对象，可能是字符串、Timestamp等

        Returns:
            int: 小时数 (0-23)
        """
        try:
            if isinstance(time_obj, str):
                # 处理 "HH:MM" 格式
                hour = int(time_obj.split(':')[0])
                return hour
            elif hasattr(time_obj, 'hour'):
                # 处理 Timestamp 或 datetime 对象
                return time_obj.hour
            else:
                # 尝试转换为字符串再解析
                time_str = str(time_obj)
                if ':' in time_str:
                    hour = int(time_str.split(':')[0])
                    return hour
            return 0
        except:
            return 0
    
    def _is_delayed_prediction(self, row) -> bool:
        """
        判断是否为预测延误航班

        预测延误定义：预测延误值 > 15分钟 或 预测离港时间与计划离港时间不在同一时段

        Args:
            row: DataFrame行数据

        Returns:
            bool: 是否为预测延误航班
        """
        try:
            # 获取预测延误值
            delay_minutes = row['预测离港延误(分钟)']

            # 获取计划和预测离港时间的小时数
            planned_hour = self._parse_time_to_hour(row['计划离港时间'])
            predicted_hour = self._parse_time_to_hour(row['预测离港时间'])

            # 判断条件：延误超过0分钟 且 跨时段
            is_significant_delay = delay_minutes > 0
            is_cross_hour = planned_hour != predicted_hour

            return is_significant_delay and is_cross_hour

        except Exception as e:
            print(f"预测延误判断错误: {e}")
            return False
    
    def _is_delayed_actual(self, row) -> bool:
        """
        判断是否为实际延误航班

        实际延误定义：(实际离港时间 - 计划离港时间) > 15分钟 或 两者不在同一时段

        Args:
            row: DataFrame行数据

        Returns:
            bool: 是否为实际延误航班
        """
        try:
            # 获取时间对象
            planned_time = row['计划离港时间']
            actual_time = row['实际离港时间']

            # 计算延误分钟数
            if hasattr(planned_time, 'timestamp') and hasattr(actual_time, 'timestamp'):
                # Timestamp对象
                delay_minutes = (actual_time - planned_time).total_seconds() / 60
            else:
                # 尝试其他方式计算
                delay_minutes = 0

            # 获取小时数
            planned_hour = self._parse_time_to_hour(planned_time)
            actual_hour = self._parse_time_to_hour(actual_time)

            # 判断条件：延误超过15分钟 且 跨时段
            is_significant_delay = abs(delay_minutes) > 15
            is_cross_hour = planned_hour != actual_hour

            return is_significant_delay and is_cross_hour

        except Exception as e:
            print(f"实际延误判断错误: {e}")
            return False
    
    def simulate_flight_congestion(self) -> pd.DataFrame:
        """
        进行航班积压量推演分析
        
        Returns:
            pd.DataFrame: 推演结果，包含各时段的计划离岗数、实际积压量、预测积压量
        """
        if self.delay_prediction_data is None:
            raise ValueError("请先加载延误预测数据")
        
        # 定义时段范围 (7-23点)
        time_slots = list(range(7, 23))
        
        # 初始化结果字典
        results = {
            '时段': [f"{hour:02d}:00-{hour:02d}:59" for hour in time_slots],
            '计划离岗数': [0] * len(time_slots),
            '实际积压量': [0] * len(time_slots),
            '预测积压量': [0] * len(time_slots)
        }
        
        # 遍历每条航班数据
        total_flights = len(self.delay_prediction_data)
        delayed_actual_count = 0
        delayed_prediction_count = 0

        for _, row in self.delay_prediction_data.iterrows():
            # 1. 统计计划离港数（按计划离港时间）
            planned_hour = self._parse_time_to_hour(row['计划离港时间'])
            if 7 <= planned_hour <= 22:
                slot_index = planned_hour - 7
                results['计划离岗数'][slot_index] += 1

            # 2. 统计实际积压量（按实际离港时间，实际延误航班）
            if self._is_delayed_actual(row):
                delayed_actual_count += 1
                actual_hour = self._parse_time_to_hour(row['实际离港时间'])
                if 7 <= actual_hour <= 22:
                    slot_index = actual_hour - 7
                    results['实际积压量'][slot_index] += 1

            # 3. 统计预测积压量（按预测离港时间，预测延误航班）
            if self._is_delayed_prediction(row):
                delayed_prediction_count += 1
                predicted_hour = self._parse_time_to_hour(row['预测离港时间'])
                if 7 <= predicted_hour <= 22:
                    slot_index = predicted_hour - 7
                    results['预测积压量'][slot_index] += 1

        print(f"推演统计: 总航班数={total_flights}, 实际延误={delayed_actual_count}, 预测延误={delayed_prediction_count}")
        
        # 转换为DataFrame
        simulation_df = pd.DataFrame(results)
        
        # 保存结果
        self.simulation_results = simulation_df
        
        print(f"推演完成，分析了 {len(time_slots)} 个时段的积压情况")
        
        return simulation_df
    
    def get_simulation_summary(self) -> Dict:
        """
        获取推演结果摘要
        
        Returns:
            Dict: 推演摘要信息
        """
        if self.simulation_results is None:
            return {}
        
        df = self.simulation_results
        
        summary = {
            '总计划离岗数': df['计划离岗数'].sum(),
            '总实际积压量': df['实际积压量'].sum(),
            '总预测积压量': df['预测积压量'].sum(),
            '实际积压率': f"{(df['实际积压量'].sum() / df['计划离岗数'].sum() * 100):.1f}%" if df['计划离岗数'].sum() > 0 else "0%",
            '预测积压率': f"{(df['预测积压量'].sum() / df['计划离岗数'].sum() * 100):.1f}%" if df['计划离岗数'].sum() > 0 else "0%",
            '高峰时段_实际': df.loc[df['实际积压量'].idxmax(), '时段'] if df['实际积压量'].max() > 0 else "无",
            '高峰时段_预测': df.loc[df['预测积压量'].idxmax(), '时段'] if df['预测积压量'].max() > 0 else "无"
        }
        
        return summary
    
    def export_simulation_results(self, file_path: str):
        """
        导出推演结果到Excel文件
        
        Args:
            file_path: 导出文件路径
        """
        if self.simulation_results is None:
            raise ValueError("没有推演结果可导出")
        
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            # 导出推演结果
            self.simulation_results.to_excel(writer, sheet_name='积压量分析', index=False)
            
            # 导出摘要信息
            summary = self.get_simulation_summary()
            summary_df = pd.DataFrame(list(summary.items()), columns=['指标', '数值'])
            summary_df.to_excel(writer, sheet_name='推演摘要', index=False)
        
        print(f"推演结果已导出到: {file_path}")


def create_flight_simulator() -> FlightSimulator:
    """
    创建航班推演器实例
    
    Returns:
        FlightSimulator: 推演器实例
    """
    return FlightSimulator()
