#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一重排班器
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加src目录到路径
sys.path.append('src')

def create_test_data():
    """创建测试数据"""
    # 创建模拟的延误预测数据
    test_data = []
    base_time = datetime.now().replace(hour=7, minute=0, second=0, microsecond=0)
    
    for i in range(10):
        scheduled_time = base_time + timedelta(minutes=i*30)
        actual_time = scheduled_time + timedelta(minutes=np.random.randint(0, 120))  # 随机延误0-120分钟
        predicted_time = scheduled_time + timedelta(minutes=np.random.randint(0, 100))  # 预测延误0-100分钟
        
        test_data.append({
            '航班号': f'CZ{3000+i}',
            '计划离港时间': scheduled_time.strftime('%H:%M'),
            '实际离港时间': actual_time.strftime('%H:%M'),
            '预测离港时间': predicted_time.strftime('%H:%M')
        })
    
    return pd.DataFrame(test_data)

def test_unified_rescheduler():
    """测试统一重排班器"""
    try:
        print("=" * 60)
        print("测试统一重排班器")
        print("=" * 60)
        
        # 导入统一重排班器
        from unified_rescheduler import UnifiedFlightRescheduler
        
        # 创建重排班器实例
        rescheduler = UnifiedFlightRescheduler()
        print("✓ 统一重排班器创建成功")
        
        # 创建测试数据
        test_df = create_test_data()
        print(f"✓ 创建测试数据: {len(test_df)} 条记录")
        print("\n测试数据预览:")
        print(test_df.head())
        
        # 执行重排班优化
        print("\n开始执行重排班优化...")
        target_date = datetime.now().strftime('%Y-%m-%d')
        
        original_df, optimized_df = rescheduler.optimize_schedule(test_df, target_date)
        
        print(f"✓ 重排班优化完成")
        print(f"  - 原始数据: {len(original_df)} 条")
        print(f"  - 优化结果: {len(optimized_df) if optimized_df is not None else 0} 条")
        
        if optimized_df is not None and not optimized_df.empty:
            print("\n优化结果预览:")
            print(optimized_df.head())
            
            # 检查结果格式
            expected_columns = ['flight_id', 'scheduled_departure', 'actual_departure', 
                              'expected_departure', 'optimized_departure']
            missing_columns = [col for col in expected_columns if col not in optimized_df.columns]
            if missing_columns:
                print(f"⚠ 缺少列: {missing_columns}")
            else:
                print("✓ 结果格式正确")
        else:
            print("⚠ 没有优化结果")
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_unified_rescheduler()
    if success:
        print("✓ 所有测试通过")
    else:
        print("✗ 测试失败")
