#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一重排班器
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加src目录到路径
sys.path.append('src')

def create_test_data():
    """创建测试数据 - 模拟延误预测的真实输出格式，包含更多延误情况"""
    # 创建模拟的延误预测数据，格式与FlightDelayPredictor.predict_flight_delays()输出一致
    test_data = []
    base_date = datetime.now().strftime('%Y-%m-%d')

    # 创建更多航班，包含严重延误的情况
    flight_schedules = [
        ('07:00', 45),   # 延误45分钟
        ('07:05', 120),  # 延误2小时
        ('07:10', 30),   # 延误30分钟
        ('07:15', 90),   # 延误1.5小时
        ('07:20', 15),   # 延误15分钟
        ('07:25', 180),  # 延误3小时
        ('07:30', 60),   # 延误1小时
        ('07:35', 240),  # 延误4小时
        ('07:40', 75),   # 延误1.25小时
        ('07:45', 150),  # 延误2.5小时
        ('08:00', 300),  # 延误5小时
        ('08:05', 20),   # 延误20分钟
        ('08:10', 480),  # 延误8小时
        ('08:15', 100),  # 延误1.67小时
        ('08:20', 360),  # 延误6小时
    ]

    for i, (scheduled_time_str, delay_minutes) in enumerate(flight_schedules):
        # 计算实际离港时间（假设与预测相近）
        scheduled_dt = datetime.strptime(f"{base_date} {scheduled_time_str}", '%Y-%m-%d %H:%M')
        actual_dt = scheduled_dt + timedelta(minutes=delay_minutes + np.random.randint(-10, 10))  # 实际时间有小幅波动
        predicted_dt = scheduled_dt + timedelta(minutes=delay_minutes)

        actual_time_str = actual_dt.strftime('%H:%M')
        predicted_time_str = predicted_dt.strftime('%H:%M')

        test_data.append({
            '航班号': f'CZ{3000+i}',
            '日期': base_date,
            '计划离港时间': scheduled_time_str,
            '实际离港时间': actual_time_str,
            '预测离港延误(分钟)': delay_minutes,
            '预测离港时间': predicted_time_str
        })

    return pd.DataFrame(test_data)

def test_unified_rescheduler():
    """测试统一重排班器"""
    try:
        print("=" * 60)
        print("测试统一重排班器")
        print("=" * 60)
        
        # 导入统一重排班器
        from unified_rescheduler import UnifiedFlightRescheduler
        
        # 创建重排班器实例
        rescheduler = UnifiedFlightRescheduler()
        print("✓ 统一重排班器创建成功")
        
        # 创建测试数据
        test_df = create_test_data()
        print(f"✓ 创建测试数据: {len(test_df)} 条记录")
        print("\n测试数据预览:")
        print(test_df.head())
        
        # 执行重排班优化
        print("\n开始执行重排班优化...")
        target_date = datetime.now().strftime('%Y-%m-%d')
        
        original_df, optimized_df = rescheduler.optimize_schedule(test_df, target_date)
        
        print(f"✓ 重排班优化完成")
        print(f"  - 原始数据: {len(original_df)} 条")
        print(f"  - 优化结果: {len(optimized_df) if optimized_df is not None else 0} 条")
        
        if optimized_df is not None and not optimized_df.empty:
            print("\n优化结果预览:")
            print(optimized_df.head())
            
            # 检查结果格式
            expected_columns = ['flight_id', 'scheduled_departure', 'actual_departure', 
                              'expected_departure', 'optimized_departure']
            missing_columns = [col for col in expected_columns if col not in optimized_df.columns]
            if missing_columns:
                print(f"⚠ 缺少列: {missing_columns}")
            else:
                print("✓ 结果格式正确")
        else:
            print("⚠ 没有优化结果")
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_unified_rescheduler()
    if success:
        print("✓ 所有测试通过")
    else:
        print("✗ 测试失败")
