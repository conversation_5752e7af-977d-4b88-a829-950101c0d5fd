#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的打包脚本
"""

import os
import sys
import subprocess
import shutil

def create_build_spec():
    """创建简单的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('src', 'src')],
    hiddenimports=[
        'PyQt5.QtCore', 'PyQt5.QtGui', 'PyQt5.QtWidgets',
        'pandas', 'numpy', 'matplotlib', 'seaborn', 'openpyxl',
        'matplotlib.backends.backend_qt5agg'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['tkinter'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='航班流量预测系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('build.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✓ 创建spec文件")

def build_with_pyqt_env():
    """使用pyqt环境打包"""
    pyqt_python = r"D:/Software/Anaconda/envs/pyqt/python.exe"
    
    if not os.path.exists(pyqt_python):
        print("✗ 找不到pyqt环境的python")
        return False
    
    print(f"使用pyqt环境: {pyqt_python}")
    
    # 创建spec文件
    create_build_spec()
    
    # 运行打包
    cmd = [pyqt_python, '-m', 'PyInstaller', '--clean', 'build.spec']
    print(f"执行: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, cwd=os.getcwd(), timeout=300)
        
        if result.returncode == 0:
            print("✓ 打包成功")
            
            # 检查文件
            exe_path = os.path.join('dist', '航班流量预测系统.exe')
            if os.path.exists(exe_path):
                size_mb = os.path.getsize(exe_path) / (1024 * 1024)
                print(f"✓ exe文件大小: {size_mb:.1f} MB")
                
                return True
            else:
                print("✗ 未找到exe文件")
                return False
        else:
            print(f"✗ 打包失败，返回码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 打包超时")
        return False
    except Exception as e:
        print(f"✗ 打包出错: {e}")
        return False

def main():
    print("=" * 40)
    print("简化打包工具")
    print("=" * 40)
    
    if build_with_pyqt_env():
        print("\n🎉 打包完成！")
        print("文件位置: dist/航班流量预测系统.exe")
        return 0
    else:
        print("\n❌ 打包失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
