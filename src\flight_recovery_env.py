#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
航班恢复环境类
从predict.py移植的FlightRecoveryEnv类
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def delay_cost_caculate(delay, passenger_count):
    """延误成本计算函数（与predict.py一致）"""
    return np.where(delay < 240, 0,
                    np.where(delay <= 480, 200 * passenger_count,
                             400 * passenger_count)).astype(int)

class FlightRecoveryEnv:
    """航班恢复环境类（从predict.py移植）"""
    
    def __init__(
        self, flights_df, slots,
        available_slots, slot_length=5, max_per_slot=5,
        global_slot_key2idx=None, global_slot_counts=None,
        slot_reward_pos=1.0, slot_reward_neg=-0.7,
        cancel_penalty=-2000,
        allow_cancel=True, 
        use_expected_depature=False   
    ):
        self.flights_df = flights_df.reset_index(drop=True)
        self.slots = slots
        self.available_slots = available_slots
        self.slot_length = slot_length
        self.max_per_slot = max_per_slot

        self.slot_counts = [0 for _ in self.slots]
        self.flight_assignments = [-1 for _ in range(len(self.flights_df))]
        self.state = (tuple(self.flight_assignments), tuple(self.slot_counts))
        self.done = False

        self.global_slot_key2idx = global_slot_key2idx
        self.global_slot_counts = global_slot_counts

        self.use_expected_depature = use_expected_depature

        self.original_delays = []
        for idx, flight in self.flights_df.iterrows():
            sch_dep = flight['scheduled_departure']
            if self.use_expected_depature and 'expected_departure' in flight:
                actual_dep = flight['expected_departure']
            else:
                actual_dep = flight['actual_departure']
            delay = (actual_dep - sch_dep).total_seconds() / 60.0 - 15
            self.original_delays.append(max(delay, 0))

        self.slot_reward_pos = slot_reward_pos
        self.slot_reward_neg = slot_reward_neg
        self.cancel_penalty = cancel_penalty
        self.allow_cancel = allow_cancel  

    def get_action_mask(self):
        """获取动作掩码（与predict.py完全一致）"""
        n_flight = len(self.flights_df)
        n_slot = len(self.slots)
        mask = np.zeros((n_flight, n_slot + 1), dtype=np.bool_)
        for idx, assigned in enumerate(self.flight_assignments):
            if assigned == -1:
                flight = self.flights_df.iloc[idx]
                sch_dep = flight['scheduled_departure']
                if self.use_expected_depature and 'expected_departure' in flight:
                    actual_dep = flight['expected_departure']
                else:
                    actual_dep = flight['actual_departure']
                for slot_idx, slot in enumerate(self.slots):
                    # 检查时隙是否在实际离港时间+20分钟内
                    if slot['slot_start'] > actual_dep + timedelta(minutes=20):
                        continue
                    # 检查时隙是否可用
                    if not self.available_slots[slot_idx]:
                        continue
                    # 检查时隙剩余容量
                    if self.get_slot_remain_capacity(slot_idx) <= 0:
                        continue
                    # 检查时隙不能早于计划离港时间-10分钟
                    if slot['slot_start'] < sch_dep - timedelta(minutes=10):
                        continue
                    mask[idx, slot_idx] = True
                if self.allow_cancel:
                    mask[idx, n_slot] = True
        return mask

    def get_slot_remain_capacity(self, slot_idx):
        """获取时隙剩余容量"""
        return self.max_per_slot - self.slot_counts[slot_idx]

    def get_assignment_results(self, use_actual_for_stats=False):
        """获取分配结果（与predict.py完全一致）"""
        results = []
        n_slot = len(self.slots)
        for idx, slot_idx in enumerate(self.flight_assignments):
            flight = self.flights_df.iloc[idx]
            result = {
                'flight_id': flight['flight_id'],
                'scheduled_departure': flight['scheduled_departure'],
                'actual_departure': flight['actual_departure'] if 'actual_departure' in flight else None,
                'expected_departure': flight['expected_departure'] if 'expected_departure' in flight else None,
                'passenger_count': flight['passenger_count']
            }
            sch_dep = flight['scheduled_departure']
            if use_actual_for_stats and 'actual_departure' in flight and 'expected_departure' in flight:
                actual_dep = flight['actual_departure']
                expected_dep = flight['expected_departure']
            elif 'expected_departure' in flight:
                actual_dep = flight['expected_departure']
                expected_dep = flight['expected_departure']
            else:
                actual_dep = flight['actual_departure']
                expected_dep = flight.get('expected_departure', None) or flight['actual_departure']

            if slot_idx == -1:
                result['new_scheduled_departure'] = sch_dep - timedelta(minutes=5)
                result['new_delay_minutes'] = max(0,(expected_dep - sch_dep).total_seconds() / 60.0 -15)
                result['new_delay_cost'] = result['new_delay_minutes'] * flight['passenger_count']
                result['cancelled'] = False
                result['origin_delay_minutes'] = max(0,(expected_dep - sch_dep).total_seconds() / 60.0 -15)
                result['origin_delay_cost'] = delay_cost_caculate(result['origin_delay_minutes'],flight['passenger_count'])
                # 真实
                result['ture_new_delay'] = max(0,(actual_dep - sch_dep).total_seconds() / 60.0 -15)
                result['ture_new_cost'] = result['ture_new_delay'] * flight['passenger_count']
                result['ture_origin_delay'] = max(0,(actual_dep - sch_dep).total_seconds() / 60.0 -15)
                result['ture_origin_cost'] = delay_cost_caculate(result['ture_origin_delay'] , flight['passenger_count'])
            elif slot_idx == n_slot or slot_idx == -2:
                result['new_scheduled_departure'] = None
                result['new_delay_minutes'] = 9999
                result['new_delay_cost'] = 9999 * flight['passenger_count']
                result['cancelled'] = True
                result['origin_delay_minutes'] = max(0,(expected_dep - sch_dep).total_seconds() / 60.0 -15)
                result['origin_delay_cost'] = result['origin_delay_minutes'] * flight['passenger_count']
                result['ture_new_delay'] = 9999
                result['ture_new_cost'] = 9999 * flight['passenger_count']
                result['ture_origin_delay'] = max(0,(actual_dep - sch_dep).total_seconds() / 60.0 -15)
                result['ture_origin_cost'] = delay_cost_caculate(result['ture_origin_delay'] , flight['passenger_count'])
            else:
                new_sch_dep = self.slots[slot_idx]['slot_start']
                delay = (expected_dep - new_sch_dep).total_seconds() / 60.0 -15
                delay = max(delay, 0)
                result['new_scheduled_departure'] = new_sch_dep
                result['new_delay_minutes'] = delay
                result['new_delay_cost'] = delay_cost_caculate(delay , flight['passenger_count'])
                result['cancelled'] = False
                result['origin_delay_minutes'] = max(0,(expected_dep - sch_dep).total_seconds() / 60.0 -15)
                result['origin_delay_cost'] = delay_cost_caculate(result['origin_delay_minutes'] , flight['passenger_count'])
                # 真实
                delay_true = (actual_dep - new_sch_dep).total_seconds() / 60.0 -15
                delay_true = max(delay_true, 0)
                result['ture_new_delay'] = delay_true
                result['ture_new_cost'] = delay_cost_caculate(delay_true , flight['passenger_count'])
                result['ture_origin_delay'] = max(0,(actual_dep - sch_dep).total_seconds() / 60.0 -15)
                result['ture_origin_cost'] = delay_cost_caculate(result['ture_origin_delay'] , flight['passenger_count'])
            results.append(result)
        return pd.DataFrame(results)

    def reset(self):
        """重置环境"""
        self.slot_counts = [0 for _ in self.slots]
        self.flight_assignments = [-1 for _ in range(len(self.flights_df))]
        self.state = (tuple(self.flight_assignments), tuple(self.slot_counts))
        self.done = False
        return self.state

    def step(self, action):
        """执行动作（与predict.py完全一致）"""
        flight_idx, slot_idx = action
        if self.flight_assignments[flight_idx] != -1:
            raise Exception(f"航班{flight_idx}已分配，不可重复分配")
        n_slot = len(self.slots)
        flight = self.flights_df.iloc[flight_idx]
        sch_dep = flight['scheduled_departure']
        passenger_count = flight['passenger_count']
        original_delay = self.original_delays[flight_idx]

        # 取消动作
        if slot_idx == n_slot:
            self.flight_assignments[flight_idx] = -2  # -2表示取消
            reward = self.cancel_penalty
            done = all([x != -1 for x in self.flight_assignments])
            self.done = done
            self.state = (tuple(self.flight_assignments), tuple(self.slot_counts))
            return self.state, reward, done

        prev_slot_count = self.slot_counts[slot_idx]
        self.flight_assignments[flight_idx] = slot_idx
        self.slot_counts[slot_idx] += 1
        new_sch_departure = self.slots[slot_idx]['slot_start']
        if (self.global_slot_key2idx is not None) and (self.global_slot_counts is not None):
            key = (self.slots[slot_idx]['slot_start'], self.slots[slot_idx]['slot_end'])
            global_idx = self.global_slot_key2idx.get(key, None)
            if global_idx is not None:
                self.global_slot_counts[global_idx] += 1

        # 计算延误奖励（与predict.py完全一致）
        if self.use_expected_depature and 'expected_departure' in flight:
            actual_dep = flight['expected_departure']
        else:
            actual_dep = flight['actual_departure']

        delay = (actual_dep - new_sch_departure).total_seconds() / 60.0 - 15
        delay = max(delay, 0)

        reward_delay = -(delay - original_delay) / 100.0

        # 计算成本奖励（与predict.py完全一致）
        if delay < 240:
            delay_cost = 0
        elif delay <= 480:
            delay_cost = int(200 * passenger_count)
        else:
            delay_cost = int(400 * passenger_count)

        if original_delay < 240:
            original_delay_cost = 0
        elif original_delay <= 480:
            original_delay_cost = int(200 * passenger_count)
        else:
            original_delay_cost = int(400 * passenger_count)

        reward_cost = -(delay_cost - original_delay_cost) / 2000.0

        # 时隙使用奖励（与predict.py完全一致）
        slot_use_reward = 0
        if prev_slot_count == 0:
            slot_use_reward = self.slot_reward_pos
        elif prev_slot_count in [1, 2, 3, 4]:
            slot_use_reward = self.slot_reward_neg

        reward = reward_delay + reward_cost + slot_use_reward

        done = all([x != -1 for x in self.flight_assignments])
        self.done = done
        self.state = (tuple(self.flight_assignments), tuple(self.slot_counts))
        return self.state, reward, done

    def get_assignment_results(self):
        """获取分配结果"""
        results = []
        for idx, assignment in enumerate(self.flight_assignments):
            flight = self.flights_df.iloc[idx]
            
            if assignment == -2:  # 取消的航班
                results.append({
                    'flight_id': flight['flight_id'],
                    'scheduled_departure': flight['scheduled_departure'],
                    'actual_departure': flight.get('actual_departure', pd.NaT),
                    'expected_departure': flight.get('expected_departure', pd.NaT),
                    'passenger_count': flight.get('passenger_count', 0),
                    'new_scheduled_departure': None,
                    'cancelled': True
                })
            elif assignment >= 0:  # 正常分配的航班
                slot = self.slots[assignment]
                results.append({
                    'flight_id': flight['flight_id'],
                    'scheduled_departure': flight['scheduled_departure'],
                    'actual_departure': flight.get('actual_departure', pd.NaT),
                    'expected_departure': flight.get('expected_departure', pd.NaT),
                    'passenger_count': flight.get('passenger_count', 0),
                    'new_scheduled_departure': slot['slot_start'],
                    'cancelled': False
                })
            else:  # 未分配的航班
                results.append({
                    'flight_id': flight['flight_id'],
                    'scheduled_departure': flight['scheduled_departure'],
                    'actual_departure': flight.get('actual_departure', pd.NaT),
                    'expected_departure': flight.get('expected_departure', pd.NaT),
                    'passenger_count': flight.get('passenger_count', 0),
                    'new_scheduled_departure': flight['scheduled_departure'],  # 保持原时间
                    'cancelled': False
                })
        
        return pd.DataFrame(results)

def delay_cost_caculate(delay, passenger_count):
    """计算延误成本"""
    return np.where(delay < 240, 0,
                    np.where(delay <= 480, 200 * passenger_count,
                             400 * passenger_count)).astype(int)
