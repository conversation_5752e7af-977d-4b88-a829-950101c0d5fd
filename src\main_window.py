#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口模块
实现应用程序的主界面
"""

import sys
import os
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QTabWidget, QLabel, QPushButton, QFileDialog,
                             QTextEdit, QProgressBar, QGroupBox, QGridLayout,
                             QSpinBox, QComboBox, QDateEdit, QMessageBox,
                             QSplitter, QFrame, QListWidget, QListWidgetItem,
                             QScrollArea, QTableWidget, QTableWidgetItem, QSizePolicy, QAbstractItemView)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal, QTimer, QThreadPool, QRunnable, QObject
from PyQt5.QtGui import QFont, QPixmap, QPalette, QColor, QIcon, QTextCharFormat
import pandas as pd

try:
    from prediction_models import FlightTrafficPredictor, FlightDelayPredictor
    from simulation_models import FlightSimulator
    from visualization import ChartWidget, PredictionChartWidget
except ImportError:
    from .prediction_models import FlightTrafficPredictor, FlightDelayPredictor
    from .simulation_models import FlightSimulator
    from .visualization import ChartWidget, PredictionChartWidget


class ImageLoaderSignals(QObject):
    """图片加载器信号"""
    image_loaded = pyqtSignal(str, QPixmap)  # 图片路径, 加载的图片
    loading_finished = pyqtSignal()  # 所有图片加载完成
    progress_updated = pyqtSignal(int, int)  # 当前进度, 总数


class ImageLoader(QRunnable):
    """异步图片加载器"""

    def __init__(self, image_paths, target_size=(260, 210)):
        super().__init__()
        self.image_paths = image_paths
        self.target_size = target_size
        self.signals = ImageLoaderSignals()
        self._is_cancelled = False

    def cancel(self):
        """取消加载"""
        self._is_cancelled = True

    def run(self):
        """执行图片加载"""
        total_count = len(self.image_paths)

        for i, image_path in enumerate(self.image_paths):
            if self._is_cancelled:
                break

            try:
                # 加载图片
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    # 缩放图片以提高性能
                    scaled_pixmap = pixmap.scaled(
                        self.target_size[0], self.target_size[1],
                        Qt.KeepAspectRatio, Qt.SmoothTransformation
                    )
                    self.signals.image_loaded.emit(image_path, scaled_pixmap)
                else:
                    # 发送空图片信号
                    self.signals.image_loaded.emit(image_path, QPixmap())

                # 更新进度
                self.signals.progress_updated.emit(i + 1, total_count)

            except Exception as e:
                # 发送空图片信号表示加载失败
                self.signals.image_loaded.emit(image_path, QPixmap())

        if not self._is_cancelled:
            self.signals.loading_finished.emit()




class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.predictor = FlightTrafficPredictor(progress_callback=self.update_prediction_progress)
        self.current_results = None

        # 用于数据分页的变量
        self.flight_data_df = None
        self.current_page = 0
        self.rows_per_page = 50  # 每页显示50行

        # 延误预测分页变量
        self.delay_current_page = 0
        self.delay_rows_per_page = 50  # 每页显示50行，与数据管理保持一致
        self.delay_full_data = None

        # 可用日期列表
        self.available_dates = []

        # 推演器实例
        self.flight_simulator = FlightSimulator()

        # 统一重排班器实例
        try:
            from unified_rescheduler import UnifiedFlightRescheduler
            self.flight_rescheduler = UnifiedFlightRescheduler()
            print("统一重排班器初始化成功")
        except ImportError as e:
            print(f"统一重排班器初始化失败: {e}")
            self.flight_rescheduler = None

        # 图片加载相关
        self.thread_pool = QThreadPool()
        self.thread_pool.setMaxThreadCount(4)  # 限制并发线程数
        self.image_cache = {}  # 图片缓存
        self.current_image_loader = None  # 当前图片加载器
        self._current_base_folder = None  # 当前基础文件夹路径

        self.init_ui()
        self.setup_connections()

    def update_prediction_progress(self, progress):
        """更新预测进度"""
        if hasattr(self, 'progress_bar') and self.progress_bar.isVisible():
            self.progress_bar.setValue(progress)
            if progress < 30:
                self.statusBar().showMessage("正在准备数据...")
            elif progress < 80:
                self.statusBar().showMessage(f"正在进行预测... {progress}%")
            else:
                self.statusBar().showMessage("正在处理结果...")

    def setup_connections(self):
        """设置信号连接"""
        # 数据管理
        self.load_flight_btn.clicked.connect(self.load_flight_data)
        self.load_weather_btn.clicked.connect(self.load_weather_data)
        self.prev_page_btn.clicked.connect(self.prev_page)
        self.next_page_btn.clicked.connect(self.next_page)

        # 流量预测
        self.start_prediction_btn.clicked.connect(self.start_prediction)
        self.target_date.calendarWidget().activated.connect(self.highlight_available_dates)
        self.target_date.calendarWidget().selectionChanged.connect(self.highlight_available_dates)

        # 延误预测
        self.start_delay_prediction_btn.clicked.connect(self.start_delay_prediction)
        self.delay_target_date.calendarWidget().activated.connect(self.highlight_available_dates)
        self.delay_target_date.calendarWidget().selectionChanged.connect(self.highlight_available_dates)
        self.delay_prev_page_btn.clicked.connect(self.delay_prev_page)
        self.delay_next_page_btn.clicked.connect(self.delay_next_page)

        # 运行推演
        self.start_simulation_btn.clicked.connect(self.start_flight_simulation)

    def load_flight_data(self):
        """加载航班数据"""
        from PyQt5.QtWidgets import QFileDialog

        # 设置默认路径为data目录
        default_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
        if not os.path.exists(default_dir):
            default_dir = ""

        # 直接让用户选择文件，支持CSV和Excel格式
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择航班数据文件", default_dir,
            "支持的数据文件 (*.csv *.xlsx *.xls);;CSV文件 (*.csv);;Excel文件 (*.xlsx *.xls);;所有文件 (*)"
        )

        if file_path:
            try:
                # 显示进度条
                self.progress_bar.setVisible(True)
                self.progress_bar.setValue(0)
                self.statusBar().showMessage("正在加载航班数据...")

                # 加载数据到预测器
                self.predictor.load_data(file_path)

                # 更新进度
                self.progress_bar.setValue(50)
                self.statusBar().showMessage("正在处理数据...")

                # 更新界面显示
                filename = os.path.basename(file_path)
                self.flight_file_label.setText(filename)

                # 加载数据到表格预览
                self.load_flight_data_to_table()

                # 完成
                self.progress_bar.setValue(100)
                self.statusBar().showMessage(f"航班数据加载成功: {filename}")

                # 延迟隐藏进度条
                QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))

                # 更新可用日期
                self.update_available_dates()

            except Exception as e:
                self.progress_bar.setVisible(False)
                QMessageBox.critical(self, "错误", f"加载航班数据失败: {str(e)}")

    def load_flight_data_to_table(self):
        """将航班数据加载到表格中进行预览"""
        if self.predictor.historical_data is None:
            return

        df = self.predictor.historical_data

        # 设置表格
        self.flight_data_table.setRowCount(min(len(df), self.rows_per_page))
        self.flight_data_table.setColumnCount(len(df.columns))
        self.flight_data_table.setHorizontalHeaderLabels(df.columns.tolist())

        # 重置分页
        self.current_page = 0
        self.flight_data_df = df

        # 更新表格内容
        self.update_flight_data_table()

    def update_flight_data_table(self):
        """更新航班数据表格显示"""
        self._update_table_page(
            table_widget=self.flight_data_table,
            full_df=self.flight_data_df,
            current_page=self.current_page,
            rows_per_page=self.rows_per_page,
            page_label=self.page_label,
            prev_button=self.prev_page_btn,
            next_button=self.next_page_btn
        )

    def update_available_dates(self):
        """更新可用日期列表并刷新日历"""
        self.available_dates = self.predictor.get_available_dates()
        if not self.available_dates:
            return

        # 更新流量预测日历
        self.highlight_available_dates(self.target_date)
        # 更新延误预测日历
        self.highlight_available_dates(self.delay_target_date)

    def highlight_available_dates(self, date_edit_widget=None):
        """高亮日历中的可用日期"""
        if not self.available_dates:
            return

        # 如果没有指定窗口，则两个都更新
        widgets = [self.target_date, self.delay_target_date] if date_edit_widget is None else [date_edit_widget]

        for widget in widgets:
            calendar = widget.calendarWidget()

            # 设置有效日期格式
            valid_format = QTextCharFormat()
            valid_format.setBackground(QColor(200, 255, 200))  # 浅绿色背景
            valid_format.setFontWeight(QFont.Bold)

            # 设置无效日期格式
            invalid_format = QTextCharFormat()
            invalid_format.setForeground(Qt.lightGray)
            invalid_format.setFontStrikeOut(True)

            # 将所有日期重置为无效格式
            start_date = QDate.fromString(min(self.available_dates), "yyyy-MM-dd").addMonths(-1)
            end_date = QDate.fromString(max(self.available_dates), "yyyy-MM-dd").addMonths(1)
            current_date = start_date
            while current_date <= end_date:
                calendar.setDateTextFormat(current_date, invalid_format)
                current_date = current_date.addDays(1)

            # 设置有效日期的格式
            for date_str in self.available_dates:
                q_date = QDate.fromString(date_str, "yyyy-MM-dd")
                calendar.setDateTextFormat(q_date, valid_format)

    def load_weather_data(self):
        """加载天气数据"""
        from PyQt5.QtWidgets import QFileDialog, QMessageBox

        # 提供选择：文件夹或文件
        reply = QMessageBox.question(
            self, '选择加载方式',
            '请选择天气数据加载方式：\n\n点击"Yes"选择文件夹（推荐）\n点击"No"选择单个或多个文件',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            # 选择文件夹
            folder_path = QFileDialog.getExistingDirectory(self, "选择天气数据文件夹")
            if folder_path:
                self.load_weather_from_folder(folder_path)
        else:
            # 选择文件
            file_paths, _ = QFileDialog.getOpenFileNames(
                self, "选择天气数据文件", "",
                "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif *.tiff);;所有文件 (*)"
            )
            if file_paths:
                self.load_weather_from_files(file_paths)

    def prev_page(self):
        """上一页"""
        if self.current_page > 0:
            self.current_page -= 1
            self.update_flight_data_table()

    def next_page(self):
        """下一页"""
        if self.flight_data_df is not None:
            total_pages = (len(self.flight_data_df) - 1) // self.rows_per_page + 1
            if self.current_page < total_pages - 1:
                self.current_page += 1
                self.update_flight_data_table()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("航班流量预测与运行状态推演系统 v1.0")
        self.setGeometry(100, 100, 1400, 900)

        # 设置窗口图标
        icon_path = os.path.join(os.path.dirname(__file__), '..', 'resources', 'app_icon.png')
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #0078d4;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标题
        title_label = QLabel("广州白云机场航班流量预测与运行状态推演系统")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setStyleSheet("color: #0078d4; margin: 10px;")
        main_layout.addWidget(title_label)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建各个选项卡
        self.create_data_tab()
        self.create_prediction_tab()
        self.create_delay_prediction_tab()
        self.create_simulation_tab()
        self.create_rescheduling_tab()
        
        # 创建状态栏
        self.statusBar().showMessage("就绪")
        
        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.statusBar().addPermanentWidget(self.progress_bar)
    
    def create_data_tab(self):
        """创建数据管理选项卡"""
        data_widget = QWidget()
        layout = QVBoxLayout(data_widget)
        
        # 数据文件管理组
        file_group = QGroupBox("数据文件管理")
        file_layout = QGridLayout(file_group)
        
        # 航班数据文件
        file_layout.addWidget(QLabel("航班数据文件:"), 0, 0)
        self.flight_file_label = QLabel("未选择文件")
        self.flight_file_label.setStyleSheet("border: 1px solid #ccc; padding: 5px;")
        file_layout.addWidget(self.flight_file_label, 0, 1)
        
        self.load_flight_btn = QPushButton("选择航班数据")
        file_layout.addWidget(self.load_flight_btn, 0, 2)
        
        # 天气数据文件
        file_layout.addWidget(QLabel("天气数据文件:"), 1, 0)
        self.weather_file_label = QLabel("未选择文件")
        self.weather_file_label.setStyleSheet("border: 1px solid #ccc; padding: 5px;")
        file_layout.addWidget(self.weather_file_label, 1, 1)
        
        self.load_weather_btn = QPushButton("选择天气数据")
        file_layout.addWidget(self.load_weather_btn, 1, 2)
        
        layout.addWidget(file_group)

        # 数据预览组
        preview_group = QGroupBox("数据预览")
        preview_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        preview_layout = QVBoxLayout(preview_group)

        # 创建选项卡用于不同类型的预览
        self.preview_tabs = QTabWidget()
        self.preview_tabs.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 航班数据预览
        flight_preview_widget = QWidget()
        flight_preview_layout = QVBoxLayout(flight_preview_widget)
        flight_preview_layout.setContentsMargins(0, 0, 0, 0)

        self.flight_data_table = QTableWidget()
        font = QFont()
        font.setPointSize(8)
        self.flight_data_table.setFont(font)
        self.flight_data_table.setAlternatingRowColors(True)
        self.flight_data_table.setStyleSheet('''
            QTableWidget {
                gridline-color: #dcdcdc;
                selection-background-color: #e0e0e0;
                selection-color: #333;
            }
            QHeaderView::section {
                background-color: #0078d4;
                color: white;
                padding: 4px;
                border: 1px solid #006bbd;
                font-weight: bold;
            }
        ''')
        
        pagination_widget = QWidget()
        pagination_layout = QHBoxLayout(pagination_widget)
        self.prev_page_btn = QPushButton("<< 上一页")
        self.next_page_btn = QPushButton("下一页 >>")
        self.page_label = QLabel("请先加载数据")
        self.page_label.setAlignment(Qt.AlignCenter)

        pagination_layout.addWidget(self.prev_page_btn)
        pagination_layout.addStretch()
        pagination_layout.addWidget(self.page_label)
        pagination_layout.addStretch()
        pagination_layout.addWidget(self.next_page_btn)

        flight_preview_layout.addWidget(self.flight_data_table)
        flight_preview_layout.addWidget(pagination_widget)

        self.preview_tabs.addTab(flight_preview_widget, "航班数据")

        # 天气数据预览
        weather_preview_widget = QWidget()
        weather_preview_layout = QVBoxLayout(weather_preview_widget)
        weather_preview_layout.setContentsMargins(0, 0, 0, 0)

        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setMinimumHeight(300)

        self.image_container = QWidget()
        self.image_container_layout = QHBoxLayout(self.image_container)
        self.image_container_layout.setSpacing(10)
        scroll_area.setWidget(self.image_container)

        weather_preview_layout.addWidget(scroll_area)

        self.preview_tabs.addTab(weather_preview_widget, "天气数据")

        

        preview_layout.addWidget(self.preview_tabs)
        layout.addWidget(preview_group)

        # 移除弹性空间，让预览组占满剩余空间
        
        self.tab_widget.addTab(data_widget, "数据管理")
    
    def create_prediction_tab(self):
        """创建预测配置选项卡"""
        prediction_widget = QWidget()
        layout = QVBoxLayout(prediction_widget)

        # 预测参数组
        param_group = QGroupBox("预测参数设置")
        param_layout = QGridLayout(param_group)

        # 目标日期
        param_layout.addWidget(QLabel("预测日期:"), 0, 0)
        self.target_date = QDateEdit()
        self.target_date.setDate(QDate.currentDate().addDays(1))
        self.target_date.setCalendarPopup(True)
        param_layout.addWidget(self.target_date, 0, 1)

        # 修改为固定的运行时段
        param_layout.addWidget(QLabel("运行时段:"), 1, 0)
        self.time_display_label = QLabel("7:00 - 22:59")
        # 设置样式使其看起来像输入框但不可编辑
        self.time_display_label.setStyleSheet("""
            QLabel {
                border: 1px solid #c0c0c0;
                border-radius: 3px;
                padding: 5px 8px;
                background-color: #f0f0f0;
                color: #333;
                font-size: 9pt;
                min-height: 20px;
            }
        """)
        param_layout.addWidget(self.time_display_label, 1, 1)

        layout.addWidget(param_group)

        # 开始预测按钮
        self.start_prediction_btn = QPushButton("开始流量预测")
        self.start_prediction_btn.setMinimumHeight(40)
        layout.addWidget(self.start_prediction_btn)

        # 流量预测结果展示组
        results_group = QGroupBox("流量预测结果")
        results_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        results_layout = QVBoxLayout(results_group)

        # 创建选项卡用于表格和图表展示
        self.prediction_tabs = QTabWidget()
        self.prediction_tabs.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 预测数据表格
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)
        table_layout.setContentsMargins(0, 0, 0, 0)

        self.prediction_table = QTableWidget()
        font = QFont()
        font.setPointSize(8)
        self.prediction_table.setFont(font)
        self.prediction_table.setAlternatingRowColors(True)
        self.prediction_table.setStyleSheet('''
            QTableWidget {
                gridline-color: #dcdcdc;
                selection-background-color: #e0e0e0;
                selection-color: #333;
            }
            QHeaderView::section {
                background-color: #0078d4;
                color: white;
                padding: 4px;
                border: 1px solid #006bbd;
                font-weight: bold;
            }
        ''')

        table_layout.addWidget(self.prediction_table)
        self.prediction_tabs.addTab(table_widget, "预测数据表格")

        # 预测结果图表
        self.prediction_chart_widget = PredictionChartWidget()
        self.prediction_tabs.addTab(self.prediction_chart_widget, "预测结果图表")

        # 宏观指标结果
        from visualization import ChartWidget
        self.macro_indicators_widget = ChartWidget()
        self.prediction_tabs.addTab(self.macro_indicators_widget, "宏观指标结果")

        results_layout.addWidget(self.prediction_tabs)
        layout.addWidget(results_group)

        self.tab_widget.addTab(prediction_widget, "流量预测")

    def create_delay_prediction_tab(self):
        """创建延误预测选项卡"""
        delay_widget = QWidget()
        layout = QVBoxLayout(delay_widget)

        # 预测参数设置组
        param_group = QGroupBox("预测参数设置")
        param_layout = QGridLayout(param_group)

        # 预测日期
        param_layout.addWidget(QLabel("预测日期:"), 0, 0)
        self.delay_target_date = QDateEdit()
        self.delay_target_date.setDate(QDate.currentDate())
        self.delay_target_date.setCalendarPopup(True)
        param_layout.addWidget(self.delay_target_date, 0, 1)

        # 延误阈值设置（固定为15分钟）
        param_layout.addWidget(QLabel("延误阈值(分钟):"), 1, 0)
        self.delay_threshold_label = QLabel("15 分钟")
        # 设置样式使其看起来像输入框但不可编辑，与流量预测保持一致
        self.delay_threshold_label.setStyleSheet("""
            QLabel {
                border: 1px solid #c0c0c0;
                border-radius: 3px;
                padding: 5px 8px;
                background-color: #f0f0f0;
                color: #333;
                font-size: 9pt;
                min-height: 20px;
            }
        """)
        param_layout.addWidget(self.delay_threshold_label, 1, 1)

        layout.addWidget(param_group)

        # 开始预测按钮
        self.start_delay_prediction_btn = QPushButton("开始延误预测")
        self.start_delay_prediction_btn.setMinimumHeight(40)
        layout.addWidget(self.start_delay_prediction_btn)

        # 延误预测结果展示组
        results_group = QGroupBox("延误预测结果")
        results_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        results_layout = QVBoxLayout(results_group)

        # 直接创建表格布局，不使用选项卡
        table_layout = QVBoxLayout()

        self.delay_prediction_table = QTableWidget()
        # 设置表格样式，与数据管理模块完全保持一致
        font = QFont()
        font.setPointSize(8)
        self.delay_prediction_table.setFont(font)
        self.delay_prediction_table.setAlternatingRowColors(True)
        self.delay_prediction_table.setStyleSheet('''
            QTableWidget {
                gridline-color: #dcdcdc;
                selection-background-color: #e0e0e0;
                selection-color: #333;
            }
            QHeaderView::section {
                background-color: #0078d4;
                color: white;
                padding: 4px;
                border: 1px solid #006bbd;
                font-weight: bold;
            }
        ''')

        table_layout.addWidget(self.delay_prediction_table)

        # 添加分页控件
        delay_pagination_widget = QWidget()
        delay_pagination_layout = QHBoxLayout(delay_pagination_widget)
        self.delay_prev_page_btn = QPushButton("<< 上一页")
        self.delay_page_info_label = QLabel("请先进行延误预测")
        self.delay_page_info_label.setAlignment(Qt.AlignCenter)
        self.delay_next_page_btn = QPushButton("下一页 >>")

        delay_pagination_layout.addWidget(self.delay_prev_page_btn)
        delay_pagination_layout.addStretch()
        delay_pagination_layout.addWidget(self.delay_page_info_label)
        delay_pagination_layout.addStretch()
        delay_pagination_layout.addWidget(self.delay_next_page_btn)

        table_layout.addWidget(delay_pagination_widget)

        # 直接将表格布局添加到结果组中
        results_layout.addLayout(table_layout)
        layout.addWidget(results_group)

        self.tab_widget.addTab(delay_widget, "延误预测")

    def create_simulation_tab(self):
        """创建运行推演选项卡"""
        simulation_widget = QWidget()
        layout = QVBoxLayout(simulation_widget)

        # 推演参数设置组
        param_group = QGroupBox("推演参数设置")
        param_layout = QGridLayout(param_group)

        # 预留参数设置位置
        param_layout.addWidget(QLabel("参数设置区域（预留）"), 0, 0)
        param_placeholder = QLabel("暂无参数需要设置")
        param_placeholder.setStyleSheet("""
            QLabel {
                border: 1px solid #c0c0c0;
                border-radius: 3px;
                padding: 20px;
                background-color: #f8f9fa;
                color: #666;
                font-size: 9pt;
                text-align: center;
            }
        """)
        param_layout.addWidget(param_placeholder, 0, 1)

        layout.addWidget(param_group)

        # 开始推演按钮
        self.start_simulation_btn = QPushButton("开始推演")
        self.start_simulation_btn.setMinimumHeight(40)
        self.start_simulation_btn.setEnabled(False)  # 初始禁用，需要先进行延误预测
        self.start_simulation_btn.clicked.connect(self.start_flight_simulation)
        layout.addWidget(self.start_simulation_btn)

        # 推演结果展示组
        results_group = QGroupBox("推演结果")
        results_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        results_layout = QHBoxLayout(results_group)

        # 左侧：推演结果表格
        left_frame = QFrame()
        left_frame.setFrameStyle(QFrame.StyledPanel)
        left_layout = QVBoxLayout(left_frame)

        self.simulation_table = QTableWidget()
        # 设置表格样式，与延误预测表格保持一致
        font = QFont()
        font.setPointSize(8)
        self.simulation_table.setFont(font)
        self.simulation_table.setAlternatingRowColors(True)
        self.simulation_table.setStyleSheet('''
            QTableWidget {
                gridline-color: #dcdcdc;
                selection-background-color: #e0e0e0;
                selection-color: #333;
            }
            QHeaderView::section {
                background-color: #0078d4;
                color: white;
                padding: 4px;
                border: 1px solid #006bbd;
                font-weight: bold;
            }
        ''')

        left_layout.addWidget(self.simulation_table)
        results_layout.addWidget(left_frame)

        # 右侧：积压分析框图
        right_frame = QFrame()
        right_frame.setFrameStyle(QFrame.StyledPanel)
        right_layout = QVBoxLayout(right_frame)

        # 导入积压分析组件
        from visualization import BacklogAnalysisWidget
        self.backlog_analysis_widget = BacklogAnalysisWidget()
        right_layout.addWidget(self.backlog_analysis_widget)

        results_layout.addWidget(right_frame)

        # 设置左右比例 (表格:框图 = 8:9)
        results_layout.setStretchFactor(left_frame, 8)
        results_layout.setStretchFactor(right_frame, 9)

        layout.addWidget(results_group)

        self.tab_widget.addTab(simulation_widget, "运行推演")

    def create_rescheduling_tab(self):
        """创建重排班选项卡"""
        rescheduling_widget = QWidget()
        layout = QVBoxLayout(rescheduling_widget)

        # 重排班参数设置组
        param_group = QGroupBox("重排班参数设置")
        param_layout = QGridLayout(param_group)

        # 优化日期（根据延误预测日期自动设置，不可编辑）
        param_layout.addWidget(QLabel("优化日期:"), 0, 0)
        self.rescheduling_date_label = QLabel("请先进行延误预测")
        # 设置样式使其看起来像输入框但不可编辑，与流量预测保持一致
        self.rescheduling_date_label.setStyleSheet("""
            QLabel {
                border: 1px solid #c0c0c0;
                border-radius: 3px;
                padding: 5px 8px;
                background-color: #f0f0f0;
                color: #333;
                font-size: 9pt;
                min-height: 20px;
            }
        """)
        param_layout.addWidget(self.rescheduling_date_label, 0, 1)

        layout.addWidget(param_group)

        # 开始重排班按钮
        self.start_rescheduling_btn = QPushButton("开始重排班优化")
        self.start_rescheduling_btn.setMinimumHeight(40)
        self.start_rescheduling_btn.setEnabled(False)  # 初始禁用，需要先进行延误预测
        self.start_rescheduling_btn.clicked.connect(self.start_rescheduling_optimization)
        layout.addWidget(self.start_rescheduling_btn)

        # 重排班结果展示组
        results_group = QGroupBox("重排班结果")
        results_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        results_layout = QVBoxLayout(results_group)

        # 创建选项卡用于表格和图表展示
        self.rescheduling_tabs = QTabWidget()
        self.rescheduling_tabs.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 重排班表格
        table_widget = QWidget()
        table_layout = QVBoxLayout(table_widget)
        table_layout.setContentsMargins(0, 0, 0, 0)

        self.rescheduling_table = QTableWidget()
        font = QFont()
        font.setPointSize(8)
        self.rescheduling_table.setFont(font)
        self.rescheduling_table.setAlternatingRowColors(True)
        self.rescheduling_table.setStyleSheet('''
            QTableWidget {
                gridline-color: #dcdcdc;
                selection-background-color: #e0e0e0;
                selection-color: #333;
            }
            QHeaderView::section {
                background-color: #0078d4;
                color: white;
                padding: 4px;
                border: 1px solid #006bbd;
                font-weight: bold;
            }
        ''')

        table_layout.addWidget(self.rescheduling_table)
        self.rescheduling_tabs.addTab(table_widget, "重排班表格")

        # 优化效果图表
        try:
            from visualization import ReschedulingChartWidget
            self.rescheduling_chart_widget = ReschedulingChartWidget()
            self.rescheduling_tabs.addTab(self.rescheduling_chart_widget, "优化效果")
        except ImportError:
            print("警告: ReschedulingChartWidget未找到，将在后续添加")

        results_layout.addWidget(self.rescheduling_tabs)
        layout.addWidget(results_group)

        self.tab_widget.addTab(rescheduling_widget, "重排班")

    def display_current_page(self):
        """显示当前页的数据"""
        if self.flight_data_df is None:
            return

        total_rows = len(self.flight_data_df)
        total_pages = (total_rows + self.rows_per_page - 1) // self.rows_per_page

        start_row = self.current_page * self.rows_per_page
        end_row = min(start_row + self.rows_per_page, total_rows)

        page_df = self.flight_data_df.iloc[start_row:end_row]

        self.flight_data_table.setRowCount(len(page_df))
        self.flight_data_table.setColumnCount(len(page_df.columns))
        self.flight_data_table.setHorizontalHeaderLabels(page_df.columns)

        for row in range(len(page_df)):
            for col in range(len(page_df.columns)):
                item = QTableWidgetItem(str(page_df.iat[row, col]))
                self.flight_data_table.setItem(row, col, item)

        self.flight_data_table.resizeColumnsToContents()
        self.page_label.setText(f"第 {self.current_page + 1} / {total_pages} 页")

        # 更新按钮状态
        self.prev_page_btn.setEnabled(self.current_page > 0)
        self.next_page_btn.setEnabled(self.current_page < total_pages - 1)

    def prev_page(self):
        """切换到上一页"""
        if self.current_page > 0:
            self.current_page -= 1
            self.display_current_page()

    def next_page(self):
        """切换到下一页"""
        if self.flight_data_df is not None:
            total_rows = len(self.flight_data_df)
            total_pages = (total_rows + self.rows_per_page - 1) // self.rows_per_page
            if self.current_page < total_pages - 1:
                self.current_page += 1
                self.display_current_page()

    def load_weather_data(self):
        """加载天气数据（支持文件夹和子文件夹结构）"""
        # 创建一个更清晰的对话框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("选择加载方式")
        msg_box.setText("请选择加载天气数据的方式：")
        msg_box.setIcon(QMessageBox.Question)

        # 添加自定义按钮
        folder_button = msg_box.addButton("选择文件夹", QMessageBox.AcceptRole)
        files_button = msg_box.addButton("选择文件", QMessageBox.AcceptRole)

        msg_box.exec_()

        clicked_button = msg_box.clickedButton()

        if clicked_button == folder_button:
            # 用户选择加载文件夹
            folder_path = QFileDialog.getExistingDirectory(
                self, "选择天气数据文件夹", ""
            )
            if folder_path:
                self.load_weather_from_folder(folder_path)
        elif clicked_button == files_button:
            # 用户选择加载文件
            file_paths, _ = QFileDialog.getOpenFileNames(
                self, "选择天气数据文件", "",
                "图片文件 (*.png *.jpg *.jpeg *.bmp)"
            )
            if file_paths:
                self.load_weather_from_files(file_paths)
        # 如果点击了取消，则不执行任何操作

    def load_weather_from_folder(self, folder_path):
        """从文件夹加载天气数据（扫描子文件夹）"""
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.statusBar().showMessage("正在扫描文件夹...")

        try:
            # 支持的图片格式
            image_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff'}
            image_files = []

            # 更新进度
            self.progress_bar.setValue(20)
            self.statusBar().showMessage("正在扫描子文件夹...")

            # 递归扫描文件夹和子文件夹
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_ext = os.path.splitext(file)[1].lower()
                    if file_ext in image_extensions:
                        full_path = os.path.join(root, file)
                        image_files.append(full_path)

            if not image_files:
                QMessageBox.information(self, "提示", "在选择的文件夹中未找到图片文件")
                self.progress_bar.setVisible(False)
                return

            # 更新进度
            self.progress_bar.setValue(50)
            self.statusBar().showMessage(f"找到 {len(image_files)} 个图片文件，正在加载...")

            # 更新标签显示
            folder_name = os.path.basename(folder_path)
            self.weather_file_label.setText(f"文件夹: {folder_name} (共 {len(image_files)} 个图片)")

            # 更新进度
            self.progress_bar.setValue(70)
            self.statusBar().showMessage("正在生成图片预览...")

            # 设置基础文件夹路径，用于显示相对路径
            self._current_base_folder = folder_path
            self.update_weather_preview(image_files)

            # 完成
            self.progress_bar.setValue(100)
            self.statusBar().showMessage(f"天气数据加载成功，共 {len(image_files)} 个文件")

            # 延迟隐藏进度条
            QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))

        except Exception as e:
            self.progress_bar.setVisible(False)
            QMessageBox.critical(self, "错误", f"加载天气数据失败: {str(e)}")

    def load_weather_from_files(self, file_paths):
        """从选择的文件加载天气数据"""
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.statusBar().showMessage("正在加载天气数据...")

        try:
            weather_files = [os.path.basename(p) for p in file_paths]

            # 更新进度
            self.progress_bar.setValue(30)
            self.statusBar().showMessage("正在处理文件列表...")

            if len(weather_files) == 1:
                self.weather_file_label.setText(weather_files[0])
            else:
                self.weather_file_label.setText(f"已选择 {len(weather_files)} 个文件")

            # 更新进度
            self.progress_bar.setValue(60)
            self.statusBar().showMessage("正在生成图片预览...")

            # 清除基础文件夹路径（单文件模式）
            self._current_base_folder = None
            self.update_weather_preview(file_paths)

            # 完成
            self.progress_bar.setValue(100)
            self.statusBar().showMessage(f"天气数据加载成功，共 {len(file_paths)} 个文件")

            # 延迟隐藏进度条
            QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))

        except Exception as e:
            self.progress_bar.setVisible(False)
            QMessageBox.critical(self, "错误", f"加载天气数据失败: {str(e)}")

    

    def update_weather_preview(self, image_files):
        """更新天气图片预览（异步加载）"""
        # 取消之前的加载任务
        if self.current_image_loader:
            self.current_image_loader.cancel()

        # 清空容器
        self.clear_image_container()

        # 限制同时显示的图片数量以提高性能
        max_images = 50  # 最多显示50张图片
        if len(image_files) > max_images:
            self.statusBar().showMessage(f"找到 {len(image_files)} 张图片，预览前 {max_images} 张。", 5000)
            image_files = image_files[:max_images]

        # 创建占位符
        self.create_image_placeholders(image_files)

        # 启动异步加载
        self.start_async_image_loading(image_files)

        self.preview_tabs.setCurrentIndex(1)  # 确保切换到天气预览

    def create_image_placeholders(self, image_files):
        """创建图片占位符"""
        for image_path in image_files:
            # 创建占位符容器
            container = QWidget()
            container_layout = QVBoxLayout(container)
            container_layout.setSpacing(2)

            # 创建占位符图片标签
            placeholder_label = QLabel()
            placeholder_label.setAlignment(Qt.AlignCenter)
            placeholder_label.setStyleSheet("border: 1px solid #ccc; margin: 5px; background-color: #f0f0f0;")
            placeholder_label.setText("加载中...")
            placeholder_label.setMinimumSize(260, 210)
            placeholder_label.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)

            # 文件名标签
            filename_label = QLabel(os.path.basename(image_path))
            filename_label.setAlignment(Qt.AlignCenter)
            filename_label.setStyleSheet("font-size: 12px; font-weight: bold; color: #000000;")

            # 如果是从文件夹加载的，显示相对路径
            if self._current_base_folder:
                try:
                    rel_path = os.path.relpath(image_path, self._current_base_folder)
                    folder_path = os.path.dirname(rel_path)
                    if folder_path and folder_path != '.':
                        path_label = QLabel(f"📁 {folder_path}")
                        path_label.setAlignment(Qt.AlignCenter)
                        path_label.setStyleSheet("font-size: 10px; color: #666666;")
                        container_layout.addWidget(path_label)
                except:
                    pass

            container_layout.addWidget(placeholder_label)
            container_layout.addWidget(filename_label)

            # 存储占位符引用，用于后续更新
            container.setProperty("image_path", image_path)
            container.setProperty("image_label", placeholder_label)

            self.image_container_layout.addWidget(container)

    def start_async_image_loading(self, image_files):
        """启动异步图片加载"""
        # 创建图片加载器
        self.current_image_loader = ImageLoader(image_files)

        # 连接信号
        self.current_image_loader.signals.image_loaded.connect(self.on_image_loaded)
        self.current_image_loader.signals.loading_finished.connect(self.on_loading_finished)
        self.current_image_loader.signals.progress_updated.connect(self.on_loading_progress)

        # 启动加载
        self.thread_pool.start(self.current_image_loader)

    def on_image_loaded(self, image_path, pixmap):
        """处理单个图片加载完成"""
        # 查找对应的占位符
        for i in range(self.image_container_layout.count()):
            container = self.image_container_layout.itemAt(i).widget()
            if container and container.property("image_path") == image_path:
                image_label = container.property("image_label")
                if image_label:
                    if not pixmap.isNull():
                        image_label.setPixmap(pixmap)
                        image_label.setText("")
                        # 缓存图片
                        self.image_cache[image_path] = pixmap
                    else:
                        image_label.setText("加载失败")
                        image_label.setStyleSheet("border: 1px solid #ccc; margin: 5px; color: red;")
                break

    def on_loading_progress(self, current, total):
        """更新加载进度"""
        if hasattr(self, 'progress_bar') and self.progress_bar.isVisible():
            progress = int(70 + (current / total) * 30)  # 70-100%的进度
            self.progress_bar.setValue(progress)
            self.statusBar().showMessage(f"正在加载图片 {current}/{total}...")

    def on_loading_finished(self):
        """所有图片加载完成"""
        self.current_image_loader = None
        if hasattr(self, 'progress_bar'):
            self.progress_bar.setValue(100)
            self.statusBar().showMessage("图片加载完成")

    def clear_image_container(self):
        """清空图片容器"""
        while self.image_container_layout.count():
            child = self.image_container_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def add_image_to_container(self, image_path):
        """添加图片到横向容器（已弃用，使用异步加载）"""
        # 这个方法保留是为了兼容性，实际使用异步加载
        pass

    def start_prediction(self):
        """开始流量预测"""
        # 检查是否已加载航班数据
        if self.flight_data_df is None:
            QMessageBox.warning(self, "警告", "请先加载航班数据文件")
            return

        # 验证日期是否有效
        target_date_str = self.target_date.date().toString("yyyy-MM-dd")
        if self.available_dates and target_date_str not in self.available_dates:
            QMessageBox.warning(self, "日期无效", f"日期 {target_date_str} 没有可用的航班数据，请从日历中选择一个有效日期。")
            return

        try:
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.statusBar().showMessage("正在初始化预测...")

            # 获取参数
            target_date = self.target_date.date().toString("yyyy-MM-dd")

            # 检查是否有缓存
            cache_info = self.predictor.get_cache_info()
            if target_date in cache_info.get('cached_dates', []):
                self.statusBar().showMessage("发现缓存结果，快速加载中...")

            self.progress_bar.setValue(10)

            # 直接调用预测方法（内部会更新进度）
            prediction_result = self.predictor.predict_hourly_traffic(target_date)

            self.progress_bar.setValue(90)
            self.statusBar().showMessage("正在更新显示...")

            # 更新预测结果显示
            self.update_prediction_display(prediction_result)

            self.progress_bar.setValue(100)
            self.statusBar().showMessage("流量预测完成")

            # 延迟隐藏进度条
            QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))

        except Exception as e:
            self.progress_bar.setVisible(False)
            QMessageBox.critical(self, "预测错误", f"预测过程中发生错误：{str(e)}")
            self.statusBar().showMessage("预测失败")

    def update_prediction_display(self, prediction_df):
        """更新流量预测结果显示"""
        # 更新表格显示
        self.update_prediction_table(prediction_df)

        # 更新图表显示
        self.prediction_chart_widget.update_prediction_chart(prediction_df)

        # 更新宏观指标结果
        self.macro_indicators_widget.update_traffic_chart(prediction_df)

    def update_prediction_table(self, prediction_df):
        """更新预测数据表格"""
        if prediction_df is None or prediction_df.empty:
            self.prediction_table.setRowCount(0)
            self.prediction_table.setColumnCount(0)
            return

        # 设置表格行列数
        self.prediction_table.setRowCount(len(prediction_df))
        self.prediction_table.setColumnCount(len(prediction_df.columns))

        # 设置表头
        self.prediction_table.setHorizontalHeaderLabels(prediction_df.columns.tolist())

        # 填充数据
        for row in range(len(prediction_df)):
            for col in range(len(prediction_df.columns)):
                value = prediction_df.iloc[row, col]
                # 格式化数值显示
                if isinstance(value, float):
                    if col == len(prediction_df.columns) - 2:  # 偏差率列
                        item = QTableWidgetItem(f"{value:.1%}")
                    else:
                        item = QTableWidgetItem(f"{value:.3f}")
                else:
                    item = QTableWidgetItem(str(value))

                # 设置偏差>15%的行为红色
                if col == len(prediction_df.columns) - 1 and str(value) == "偏差>15%":
                    item.setBackground(QColor(255, 200, 200))

                self.prediction_table.setItem(row, col, item)

        # 调整列宽
        self.prediction_table.resizeColumnsToContents()





    



    def start_delay_prediction(self):
        """开始延误预测"""
        # 检查是否已加载航班数据
        if self.flight_data_df is None:
            QMessageBox.warning(self, "警告", "请先加载航班数据文件")
            return

        # 验证日期是否有效
        target_date_str = self.delay_target_date.date().toString("yyyy-MM-dd")
        if self.available_dates and target_date_str not in self.available_dates:
            QMessageBox.warning(self, "日期无效", f"日期 {target_date_str} 没有可用的航班数据，请从日历中选择一个有效日期。")
            return

        try:
            # 获取参数
            target_date = self.delay_target_date.date().toString("yyyy-MM-dd")

            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(20)
            self.statusBar().showMessage("正在进行延误预测...")

            # 使用延误预测模型进行预测
            from prediction_models import FlightDelayPredictor
            delay_predictor = FlightDelayPredictor()

            # 设置雷达图数据路径（如果已加载）
            if self._current_base_folder:
                delay_predictor.set_weather_data_path(self._current_base_folder)
                print(f"已设置雷达图路径: {self._current_base_folder}")
            else:
                print("警告: 未加载雷达图数据，将使用备用预测方法")

            # 创建临时文件保存当前航班数据
            import tempfile
            import os
            with tempfile.NamedTemporaryFile(mode='w', suffix='.xlsx', delete=False) as temp_file:
                temp_path = temp_file.name

            # 保存当前数据到临时文件
            self.flight_data_df.to_excel(temp_path, index=False)

            try:
                # 加载数据并进行预测
                delay_predictor.load_data(temp_path)
                delay_result = delay_predictor.predict_flight_delays(target_date)

                # 更新延误预测结果显示
                self.update_delay_prediction_display(delay_result)

                self.progress_bar.setValue(100)
                self.statusBar().showMessage("延误预测完成")

            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.unlink(temp_path)

            # 隐藏进度条
            self.progress_bar.setVisible(False)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"延误预测失败: {str(e)}")
            self.progress_bar.setVisible(False)
            self.statusBar().showMessage("延误预测失败")

    def validate_prediction_date(self, target_date):
        """验证预测日期是否在航班数据范围内"""
        if self.flight_data_df is None or self.flight_data_df.empty:
            return False

        try:
            import pandas as pd

            # 假设航班数据中有日期列，尝试不同的可能列名
            date_columns = ['日期', 'date', '计划起飞时间', '起飞时间', 'departure_time', 'scheduled_time']
            date_column = None

            for col in date_columns:
                if col in self.flight_data_df.columns:
                    date_column = col
                    break

            if date_column is None:
                # 如果没有找到日期列，假设数据是当前日期的
                return True

            # 提取日期部分
            flight_dates = pd.to_datetime(self.flight_data_df[date_column]).dt.date
            target_date_obj = pd.to_datetime(target_date).date()

            # 检查目标日期是否在航班数据的日期范围内
            min_date = flight_dates.min()
            max_date = flight_dates.max()

            return min_date <= target_date_obj <= max_date

        except Exception:
            # 如果验证过程出错，默认允许预测
            return True





    def update_delay_prediction_display(self, delay_df):
        """更新延误预测结果显示"""
        # 保存完整数据
        self.delay_full_data = delay_df
        self.delay_current_page = 0

        # 更新表格显示（分页）
        self.display_delay_current_page()

        # 启用推演按钮
        self.start_simulation_btn.setEnabled(True)

        # 更新重排班日期并启用重排班按钮
        target_date = self.delay_target_date.date().toString("yyyy-MM-dd")
        self.rescheduling_date_label.setText(target_date)
        self.start_rescheduling_btn.setEnabled(True)

    def display_delay_current_page(self):
        """显示延误预测当前页数据"""
        self._update_table_page(
            table_widget=self.delay_prediction_table,
            full_df=self.delay_full_data,
            current_page=self.delay_current_page,
            rows_per_page=self.delay_rows_per_page,
            page_label=self.delay_page_info_label,
            prev_button=self.delay_prev_page_btn,
            next_button=self.delay_next_page_btn
        )

    def _update_table_page(self, table_widget, full_df, current_page, rows_per_page, page_label, prev_button, next_button):
        """通用表格分页更新逻辑"""
        if full_df is None or full_df.empty:
            table_widget.setRowCount(0)
            page_label.setText("第 0 / 0 页")
            prev_button.setEnabled(False)
            next_button.setEnabled(False)
            return

        total_rows = len(full_df)
        total_pages = (total_rows + rows_per_page - 1) // rows_per_page

        start_row = current_page * rows_per_page
        end_row = min(start_row + rows_per_page, total_rows)

        page_df = full_df.iloc[start_row:end_row]

        # 设置表格的行、列和表头
        table_widget.setRowCount(len(page_df))
        table_widget.setColumnCount(len(page_df.columns))
        table_widget.setHorizontalHeaderLabels(page_df.columns)

        # 更新表格内容
        table_widget.setRowCount(len(page_df))
        for i, (_, row) in enumerate(page_df.iterrows()):
            for j, value in enumerate(row):
                item = QTableWidgetItem(str(value))

                # 特殊颜色处理（仅用于延误预测表）
                if table_widget == self.delay_prediction_table:
                    if '延误' in page_df.columns[j] and '分钟' in page_df.columns[j] and isinstance(value, (int, float)):
                        if value >= 60:  # 延误超过60分钟
                            item.setBackground(QColor(255, 200, 200))  # 浅红色
                        elif value >= 15:  # 延误15-60分钟
                            item.setBackground(QColor(255, 255, 200))  # 浅黄色
                        elif value < 0:  # 提前（负延误）
                            item.setBackground(QColor(200, 255, 255))  # 浅青色
                        else:  # 正常（0-15分钟延误）
                            item.setBackground(QColor(200, 255, 200))  # 浅绿色
                
                table_widget.setItem(i, j, item)

        # 更新分页信息
        page_label.setText(f"第 {current_page + 1} / {total_pages} 页")
        prev_button.setEnabled(current_page > 0)
        next_button.setEnabled(current_page < total_pages - 1)

        # 调整列宽
        table_widget.resizeColumnsToContents()

    def delay_prev_page(self):
        """延误预测切换到上一页"""
        if self.delay_current_page > 0:
            self.delay_current_page -= 1
            self.display_delay_current_page()

    def delay_next_page(self):
        """延误预测切换到下一页"""
        if self.delay_full_data is not None:
            total_rows = len(self.delay_full_data)
            total_pages = (total_rows + self.delay_rows_per_page - 1) // self.delay_rows_per_page
            if self.delay_current_page < total_pages - 1:
                self.delay_current_page += 1
                self.display_delay_current_page()

    def start_flight_simulation(self):
        """开始航班积压量推演"""
        if self.delay_full_data is None or self.delay_full_data.empty:
            QMessageBox.warning(self, "警告", "请先进行延误预测")
            return

        try:
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.statusBar().showMessage("正在进行推演分析...")

            # 加载延误预测数据到推演器
            self.flight_simulator.load_delay_prediction_data(self.delay_full_data)
            self.progress_bar.setValue(30)

            # 执行推演分析
            simulation_results = self.flight_simulator.simulate_flight_congestion()
            self.progress_bar.setValue(70)

            # 更新推演结果显示
            self.update_simulation_display(simulation_results)
            self.progress_bar.setValue(100)

            # 切换到运行推演选项卡
            self.tab_widget.setCurrentIndex(3)  # 运行推演是第4个选项卡

            self.statusBar().showMessage("推演分析完成")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"推演分析失败: {str(e)}")
            self.statusBar().showMessage("推演分析失败")
        finally:
            # 隐藏进度条
            QTimer.singleShot(1000, lambda: self.progress_bar.setVisible(False))

    def update_simulation_display(self, simulation_df):
        """更新推演结果显示"""
        # 更新推演结果表格
        self.simulation_table.setRowCount(len(simulation_df))
        self.simulation_table.setColumnCount(len(simulation_df.columns))
        self.simulation_table.setHorizontalHeaderLabels(simulation_df.columns.tolist())

        # 填充数据
        for i, row in simulation_df.iterrows():
            for j, value in enumerate(row):
                item = QTableWidgetItem(str(value))

                # 根据数值设置颜色
                if j > 0:  # 非时段列
                    if isinstance(value, (int, float)) and value > 0:
                        if j == 2:  # 实际积压量列
                            item.setBackground(QColor(255, 200, 200))  # 浅红色
                        elif j == 3:  # 预测积压量列
                            item.setBackground(QColor(255, 255, 200))  # 浅黄色
                        else:  # 计划离岗数列
                            item.setBackground(QColor(200, 255, 200))  # 浅绿色

                self.simulation_table.setItem(i, j, item)

        # 调整列宽
        self.simulation_table.resizeColumnsToContents()

        # 更新运行推演界面中的积压分析框图
        self.backlog_analysis_widget.update_backlog_analysis(simulation_df)

    def start_rescheduling_optimization(self):
        """开始重排班优化"""
        if self.delay_full_data is None or self.delay_full_data.empty:
            QMessageBox.warning(self, "警告", "请先进行延误预测")
            return

        if self.flight_rescheduler is None:
            QMessageBox.critical(self, "错误", "重排班器未正确初始化")
            return

        try:
            # 显示进度条
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.statusBar().showMessage("正在进行重排班优化...")

            # 获取目标日期
            target_date = self.rescheduling_date_label.text()

            self.progress_bar.setValue(30)

            # 执行重排班优化
            original_df, optimized_df = self.flight_rescheduler.optimize_schedule(
                self.delay_full_data, target_date
            )

            self.progress_bar.setValue(70)

            # 更新重排班结果显示
            self.update_rescheduling_display(original_df, optimized_df)
            self.progress_bar.setValue(100)

            # 切换到重排班选项卡
            self.tab_widget.setCurrentIndex(4)  # 重排班是第5个选项卡

            self.statusBar().showMessage("重排班优化完成")

            # 隐藏进度条
            self.progress_bar.setVisible(False)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"重排班优化失败: {str(e)}")
            self.progress_bar.setVisible(False)
            self.statusBar().showMessage("重排班优化失败")

    def update_rescheduling_display(self, original_df, optimized_df):
        """更新重排班结果显示"""
        # 更新重排班表格
        if optimized_df is not None and not optimized_df.empty:
            self.update_rescheduling_table(optimized_df)

            # 更新优化效果图表
            if hasattr(self, 'rescheduling_chart_widget'):
                self.rescheduling_chart_widget.update_charts(original_df, optimized_df)
        else:
            # 如果没有优化结果，显示原始数据
            self.update_rescheduling_table(original_df)

    def update_rescheduling_table(self, rescheduling_df):
        """更新重排班数据表格"""
        if rescheduling_df is None or rescheduling_df.empty:
            self.rescheduling_table.setRowCount(0)
            self.rescheduling_table.setColumnCount(0)
            return

        # 准备显示的列和中文表头
        display_columns = ['flight_id', 'scheduled_departure', 'actual_departure', 'expected_departure', 'optimized_departure']
        chinese_headers = ['航班号', '计划离港时间', '实际离港时间', '预测离港时间', '重排班离港时间']

        # 检查哪些列存在
        available_columns = []
        available_headers = []
        for i, col in enumerate(display_columns):
            if col in rescheduling_df.columns:
                available_columns.append(col)
                available_headers.append(chinese_headers[i])

        # 如果没有optimized_departure列，使用original_departure或其他可用列
        if 'optimized_departure' not in rescheduling_df.columns:
            if 'original_departure' in rescheduling_df.columns:
                available_columns.append('original_departure')
                available_headers.append('重排班离港时间')

        # 设置表格行列数
        self.rescheduling_table.setRowCount(len(rescheduling_df))
        self.rescheduling_table.setColumnCount(len(available_columns))

        # 设置中文表头
        self.rescheduling_table.setHorizontalHeaderLabels(available_headers)

        # 填充数据
        for row in range(len(rescheduling_df)):
            for col, column_name in enumerate(available_columns):
                value = rescheduling_df.iloc[row][column_name]

                # 格式化时间显示
                if 'departure' in column_name and pd.notna(value):
                    try:
                        if isinstance(value, str):
                            # 如果是字符串，尝试解析
                            dt = pd.to_datetime(value)
                        else:
                            dt = value
                        # 格式化为 HH:MM 格式
                        formatted_value = dt.strftime('%H:%M') if pd.notna(dt) else str(value)
                    except:
                        formatted_value = str(value)
                elif isinstance(value, float):
                    formatted_value = f"{value:.2f}"
                else:
                    formatted_value = str(value)

                item = QTableWidgetItem(formatted_value)
                self.rescheduling_table.setItem(row, col, item)

        # 调整列宽
        self.rescheduling_table.resizeColumnsToContents()


