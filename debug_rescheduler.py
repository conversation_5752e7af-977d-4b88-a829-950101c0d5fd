#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试重排班器 - 深入分析算法行为
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加src目录到路径
sys.path.append('src')

def create_debug_data():
    """创建调试数据 - 包含明显需要重排班的情况"""
    test_data = []
    base_date = datetime.now().strftime('%Y-%m-%d')
    
    # 创建一个明显的拥堵情况：多个航班在同一时间段严重延误
    flight_schedules = [
        # 原计划都在07:00-07:30，但都延误到08:00-09:00
        ('07:00', 60),   # 延误1小时到08:00
        ('07:05', 65),   # 延误1小时5分钟到08:10
        ('07:10', 70),   # 延误1小时10分钟到08:20
        ('07:15', 75),   # 延误1小时15分钟到08:30
        ('07:20', 80),   # 延误1小时20分钟到08:40
        ('07:25', 85),   # 延误1小时25分钟到08:50
        ('07:30', 90),   # 延误1小时30分钟到09:00
    ]
    
    for i, (scheduled_time_str, delay_minutes) in enumerate(flight_schedules):
        scheduled_dt = datetime.strptime(f"{base_date} {scheduled_time_str}", '%Y-%m-%d %H:%M')
        predicted_dt = scheduled_dt + timedelta(minutes=delay_minutes)
        actual_dt = predicted_dt + timedelta(minutes=np.random.randint(-5, 5))  # 实际时间接近预测时间
        
        test_data.append({
            '航班号': f'CZ{3000+i}',
            '日期': base_date,
            '计划离港时间': scheduled_time_str,
            '实际离港时间': actual_dt.strftime('%H:%M'),
            '预测离港延误(分钟)': delay_minutes,
            '预测离港时间': predicted_dt.strftime('%H:%M')
        })
    
    return pd.DataFrame(test_data)

def debug_rescheduler():
    """调试重排班器"""
    try:
        print("=" * 80)
        print("调试重排班器 - 深入分析算法行为")
        print("=" * 80)
        
        # 导入统一重排班器
        from unified_rescheduler import UnifiedFlightRescheduler
        
        # 创建重排班器实例
        rescheduler = UnifiedFlightRescheduler()
        print("✓ 统一重排班器创建成功")
        
        # 创建调试数据
        test_df = create_debug_data()
        print(f"✓ 创建调试数据: {len(test_df)} 条记录")
        print("\n调试数据详情:")
        print(test_df.to_string(index=False))
        
        # 分析数据转换过程
        print("\n" + "="*60)
        print("数据转换分析")
        print("="*60)
        
        processed_data = rescheduler.process_delay_prediction_data(test_df)
        
        if not processed_data.empty:
            print("\n转换后的数据详情:")
            for _, row in processed_data.iterrows():
                scheduled = row['scheduled_departure']
                expected = row['expected_departure']
                delay_minutes = (expected - scheduled).total_seconds() / 60.0
                print(f"航班 {row['flight_id']}: 计划 {scheduled.strftime('%H:%M')} -> 预测 {expected.strftime('%H:%M')} (延误 {delay_minutes:.0f} 分钟)")
        
        # 执行重排班优化
        print("\n" + "="*60)
        print("重排班优化分析")
        print("="*60)
        
        target_date = datetime.now().strftime('%Y-%m-%d')
        original_df, optimized_df = rescheduler.optimize_schedule(test_df, target_date)
        
        if optimized_df is not None and not optimized_df.empty:
            print("\n重排班结果详情:")
            for _, row in optimized_df.iterrows():
                scheduled = pd.to_datetime(row['scheduled_departure'])
                optimized = pd.to_datetime(row['optimized_departure'])
                change_minutes = (optimized - scheduled).total_seconds() / 60.0
                print(f"航班 {row['flight_id']}: {scheduled.strftime('%H:%M')} -> {optimized.strftime('%H:%M')} (调整 {change_minutes:+.0f} 分钟)")
            
            # 分析优化效果
            print("\n" + "="*60)
            print("优化效果分析")
            print("="*60)
            
            # 计算时隙分布
            optimized_times = pd.to_datetime(optimized_df['optimized_departure'])
            time_slots = {}
            for time in optimized_times:
                slot = time.strftime('%H:%M')
                time_slots[slot] = time_slots.get(slot, 0) + 1
            
            print("优化后的时隙分布:")
            for slot, count in sorted(time_slots.items()):
                print(f"  {slot}: {count} 个航班")
            
            # 检查是否有改善
            original_times = pd.to_datetime(optimized_df['scheduled_departure'])
            total_change = sum(abs((pd.to_datetime(optimized_df['optimized_departure']) - original_times).dt.total_seconds() / 60.0))
            print(f"\n总调整时间: {total_change:.0f} 分钟")
            
            if total_change > 0:
                print("✓ 算法确实进行了重排班优化")
            else:
                print("⚠ 算法没有进行任何调整")
        else:
            print("✗ 没有获得重排班结果")
        
        print("\n" + "=" * 80)
        print("调试完成")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"✗ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = debug_rescheduler()
    if success:
        print("✓ 调试完成")
    else:
        print("✗ 调试失败")
