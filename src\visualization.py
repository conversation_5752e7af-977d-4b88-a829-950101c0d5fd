#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据可视化模块
实现各种图表的绘制和展示
"""

import matplotlib
# 设置matplotlib后端
try:
    matplotlib.use('Qt5Agg')
except:
    try:
        matplotlib.use('Agg')  # 无图形界面后端
    except:
        pass

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np
import pandas as pd
import seaborn as sns
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QTabWidget
from PyQt5.QtCore import Qt

# 设置matplotlib中文字体
try:
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except:
    pass  # 如果字体设置失败，使用默认字体

class ChartWidget(QWidget):
    """图表展示组件"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 创建各个图表选项卡
        self.create_traffic_chart_tab()
    
    def create_traffic_chart_tab(self):
        """创建流量预测图表选项卡"""
        self.traffic_widget = QWidget()
        layout = QVBoxLayout(self.traffic_widget)
        
        # 创建matplotlib图形
        self.traffic_figure = Figure(figsize=(10, 6))
        self.traffic_canvas = FigureCanvas(self.traffic_figure)
        layout.addWidget(self.traffic_canvas)
        
        self.tab_widget.addTab(self.traffic_widget, "流量预测")
    

    
    def update_traffic_chart(self, traffic_data):
        """更新流量预测图表 - 宏观指标展示"""
        self.traffic_figure.clear()

        # 检查数据类型，如果是DataFrame则提取宏观指标
        if hasattr(traffic_data, 'columns') and '小时偏差率' in traffic_data.columns:
            # 这是来自流量预测的DataFrame数据
            self._update_macro_indicators(traffic_data)
        else:
            # 这是原始的字典格式数据，保持原有逻辑
            self._update_traditional_chart(traffic_data)

        self.traffic_figure.tight_layout()
        self.traffic_canvas.draw()

    def _update_macro_indicators(self, prediction_df):
        """更新宏观指标展示 - 按照用户草图设计"""
        # 计算关键指标
        avg_deviation = prediction_df['小时偏差率'].mean() * 100
        over_threshold_count = len(prediction_df[prediction_df['小时偏差率'] > 0.15])

        # 创建主图用于指标展示
        ax = self.traffic_figure.add_subplot(1, 1, 1)
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 10)
        ax.axis('off')

        # 设置背景色
        ax.set_facecolor('#f8f9fa')

        # 标题
        ax.text(5, 10.3, '流量预测宏观指标', fontsize=20, fontweight='bold',
                ha='center', va='center', color='#2c3e50')

        # 定义卡片样式函数
        def draw_indicator_card(x, y, width, height, title, icon, value, status,
                              bg_color='#ffffff', border_color='#e0e0e0', text_color='#333333'):
            from matplotlib.patches import FancyBboxPatch

            # 绘制卡片背景
            card = FancyBboxPatch((x, y), width, height,
                                boxstyle="round,pad=0.1",
                                facecolor=bg_color,
                                edgecolor=border_color,
                                linewidth=2,
                                alpha=0.9)
            ax.add_patch(card)

            # 标题和图标
            ax.text(x + width/2, y + height - 0.5, f'{icon} {title}',
                   fontsize=12, fontweight='bold', ha='center', va='center', color='#555555')

            # 主要数值
            ax.text(x + width/2, y + height/2 , value,
                   fontsize=18, fontweight='bold', ha='center', va='center', color=text_color)

            # 状态标识
            ax.text(x + width/2, y + 0.5, f'({status})',
                   fontsize=10, ha='center', va='center', color=text_color)

        # 指标1：平均偏差率 (左侧) - 简化颜色方案
        is_deviation_pass = avg_deviation < 10
        deviation_bg = '#f0fff0' if is_deviation_pass else '#fff0f0'
        deviation_border = '#51cf66' if is_deviation_pass else '#ff6b6b'
        deviation_text = '#2b8a3e' if is_deviation_pass else '#e03131'
        deviation_status = '达标' if is_deviation_pass else '不达标'

        draw_indicator_card(1, 5.5, 3.5, 3, '平均偏差率', '%', f'{avg_deviation:.1f}%',
                          deviation_status, deviation_bg, deviation_border, deviation_text)

        # 指标2：超标时段数 (右侧) - 简化颜色方案
        is_threshold_pass = over_threshold_count <= 2
        threshold_bg = '#f0fff0' if is_threshold_pass else '#fff0f0'
        threshold_border = '#51cf66' if is_threshold_pass else '#ff6b6b'
        threshold_text = '#2b8a3e' if is_threshold_pass else '#e03131'
        threshold_status = '达标' if is_threshold_pass else '不达标'

        draw_indicator_card(5.5, 5.5, 3.5, 3, '超标时段数', '!', f'{over_threshold_count}个',
                          threshold_status, threshold_bg, threshold_border, threshold_text)

        # 总体评估区域 (简化为达标/不达标)
        is_overall_pass = is_deviation_pass and is_threshold_pass
        overall_status = '达标' if is_overall_pass else '不达标'

        # 简化颜色方案：只有绿色(达标)和红色(不达标)
        if is_overall_pass:
            bg_color, border_color, text_color = ('#f0fff0', '#51cf66', '#2b8a3e')
        else:
            bg_color, border_color, text_color = ('#fff0f0', '#ff6b6b', '#e03131')

        # 总体评估卡片
        from matplotlib.patches import FancyBboxPatch
        eval_card = FancyBboxPatch((1, 0.5), 8, 3.7,
                                 boxstyle="round,pad=0.15",
                                 facecolor=bg_color,
                                 edgecolor=border_color,
                                 linewidth=3,
                                 alpha=0.9)
        ax.add_patch(eval_card)

        ax.text(5, 3.2, '总体评估', fontsize=16, fontweight='bold',
                ha='center', va='center', color='#555555')
        ax.text(5, 2.0, overall_status, fontsize=22, fontweight='bold',
                ha='center', va='center', color=text_color)

        # 指标说明
        ax.text(5, 0.8, '指标要求：平均偏差率<10%，超标时段数≤2个',
                fontsize=12, ha='center', style='italic', color='#868e96')

    def _update_traditional_chart(self, traffic_data):
        """更新传统图表展示（保持原有逻辑）"""
        # 创建子图
        ax1 = self.traffic_figure.add_subplot(2, 1, 1)
        ax2 = self.traffic_figure.add_subplot(2, 1, 2)

        # 准备数据
        hours = list(range(24))
        inbound_data = [traffic_data.get(h, {'inbound': 0})['inbound'] for h in hours]
        outbound_data = [traffic_data.get(h, {'outbound': 0})['outbound'] for h in hours]
        total_data = [traffic_data.get(h, {'total': 0})['total'] for h in hours]

        # 绘制进出港流量对比
        width = 0.35
        x = np.arange(len(hours))

        ax1.bar(x - width/2, inbound_data, width, label='进港', color='skyblue', alpha=0.8)
        ax1.bar(x + width/2, outbound_data, width, label='出港', color='lightcoral', alpha=0.8)

        ax1.set_xlabel('时间 (小时)')
        ax1.set_ylabel('航班数量')
        ax1.set_title('24小时进出港流量预测')
        ax1.set_xticks(x)
        ax1.set_xticklabels([f'{h:02d}:00' for h in hours], rotation=45)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 绘制总流量趋势
        ax2.plot(hours, total_data, marker='o', linewidth=2, markersize=4, color='green')
        ax2.fill_between(hours, total_data, alpha=0.3, color='green')

        ax2.set_xlabel('时间 (小时)')
        ax2.set_ylabel('总航班数量')
        ax2.set_title('24小时总流量趋势')
        ax2.set_xticks(range(0, 24, 2))
        ax2.set_xticklabels([f'{h:02d}:00' for h in range(0, 24, 2)])
        ax2.grid(True, alpha=0.3)

        # 标记繁忙时段
        ax2.axvspan(7, 23, alpha=0.2, color='yellow', label='繁忙时段')
        ax2.legend()
    




    def _check_period_accuracy(self, actual_periods, predicted_periods):
        """检查积压时段准确性：偏差不超过1个时段"""
        if not actual_periods or not predicted_periods:
            return len(actual_periods) == len(predicted_periods)

        # 检查起始时段偏差
        actual_start = min(actual_periods)
        predicted_start = min(predicted_periods)
        start_diff = abs(actual_start - predicted_start)

        # 检查结束时段偏差
        actual_end = max(actual_periods)
        predicted_end = max(predicted_periods)
        end_diff = abs(actual_end - predicted_end)

        return start_diff <= 1 and end_diff <= 1

    def _check_duration_consistency(self, actual_periods, predicted_periods):
        """检查持续时长一致性"""
        actual_duration = len(actual_periods)
        predicted_duration = len(predicted_periods)
        return actual_duration == predicted_duration

    def _check_peak_accuracy(self, simulation_results):
        """检查积压峰值准确性：偏差不超过15%"""
        try:
            # 找到实际积压的最高峰
            actual_peak_idx = simulation_results['实际积压量'].idxmax()
            actual_peak_value = simulation_results.loc[actual_peak_idx, '实际积压量']
            actual_peak_period = simulation_results.loc[actual_peak_idx, '时段']

            # 找到预测积压的最高峰
            predicted_peak_idx = simulation_results['预测积压量'].idxmax()
            predicted_peak_value = simulation_results.loc[predicted_peak_idx, '预测积压量']
            predicted_peak_period = simulation_results.loc[predicted_peak_idx, '时段']

            # 检查峰值时段是否一致
            period_match = actual_peak_period == predicted_peak_period

            # 计算峰值偏差
            if actual_peak_value > 0:
                deviation = abs(predicted_peak_value - actual_peak_value) / actual_peak_value * 100
            else:
                deviation = 0 if predicted_peak_value == 0 else 100

            # 峰值偏差不超过15%且时段一致
            peak_accuracy = period_match and deviation <= 15

            return peak_accuracy, deviation

        except Exception:
            return False, 0.0

    def _check_latest_period(self, actual_periods, predicted_periods):
        """检查最晚运行时段一致性"""
        if not actual_periods or not predicted_periods:
            return len(actual_periods) == len(predicted_periods)

        actual_latest = max(actual_periods)
        predicted_latest = max(predicted_periods)
        return actual_latest == predicted_latest

    
    
    def clear_charts(self):
        """清空所有图表"""
        for figure in [self.traffic_figure, self.backlog_figure,
                      self.delay_figure, self.comparison_figure]:
            figure.clear()

        for canvas in [self.traffic_canvas, self.backlog_canvas,
                      self.delay_canvas, self.comparison_canvas]:
            canvas.draw()


class PredictionChartWidget(QWidget):
    """流量预测专用图表组件"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # 创建matplotlib图形
        self.figure = Figure(figsize=(12, 6), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)

    def update_prediction_chart(self, prediction_df):
        """更新流量预测结果图表"""
        try:
            # 清除之前的图表
            self.figure.clear()

            # 创建主图
            ax1 = self.figure.add_subplot(111)

            # 提取时段标签（去掉时间范围，只保留小时）
            time_labels = [label.split('-')[0] for label in prediction_df['时段']]
            x_pos = range(len(time_labels))

            # 绘制流量对比柱状图（左Y轴）
            width = 0.35
            bars1 = ax1.bar([x - width/2 for x in x_pos], prediction_df['预测总流量'], width,
                           label='预测总流量', color='skyblue', alpha=0.8)
            bars2 = ax1.bar([x + width/2 for x in x_pos], prediction_df['实际总流量'], width,
                           label='实际总流量', color='orange', alpha=0.8)

            ax1.set_xlabel('时段')
            ax1.set_ylabel('航班数量', color='black')
            ax1.set_title('每小时流量预测对比与偏差率分析')
            ax1.set_xticks(x_pos)
            ax1.set_xticklabels(time_labels, rotation=45)
            ax1.grid(True, alpha=0.3)

            # 创建右Y轴用于偏差率
            ax2 = ax1.twinx()

            # 绘制偏差率折线图（右Y轴）
            deviation_rates = prediction_df['小时偏差率'] * 100  # 转换为百分比
            line = ax2.plot(x_pos, deviation_rates, color='red', marker='o', linewidth=2,
                           markersize=4, label='偏差率', alpha=0.8)

            # 添加15%阈值线
            ax2.axhline(y=15, color='red', linestyle='--', alpha=0.6, label='15%阈值线')

            ax2.set_ylabel('偏差率 (%)', color='red')
            ax2.tick_params(axis='y', labelcolor='red')

            # 合并图例
            lines1, labels1 = ax1.get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

            # 调整布局
            self.figure.tight_layout()

            # 刷新画布
            self.canvas.draw()

        except Exception as e:
            # 错误处理
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, f'图表生成错误: {str(e)}',
                   horizontalalignment='center', verticalalignment='center',
                   transform=ax.transAxes, fontsize=12, color='red')
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
            self.canvas.draw()

    def clear_chart(self):
        """清空图表"""
        self.figure.clear()
        self.canvas.draw()


class BacklogAnalysisWidget(QWidget):
    """积压分析组件 - 推演结果评估"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)

        # 创建matplotlib图形
        self.figure = Figure(figsize=(12, 8), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)

    def update_backlog_analysis(self, simulation_results):
        """更新积压分析结果"""
        try:
            # 清除之前的图表
            self.figure.clear()

            # 计算四个关键指标
            indicators = self._calculate_indicators(simulation_results)

            # 创建主图用于指标展示
            ax = self.figure.add_subplot(1, 1, 1)
            ax.set_xlim(0, 10)
            ax.set_ylim(0, 10)
            ax.axis('off')

            # 设置背景色
            ax.set_facecolor('#f8f9fa')

            # 添加标题
            ax.text(5, 9.5, '推演积压分析指标', fontsize=16, fontweight='bold',
                   ha='center', va='center', color='#333333')

            # 定义卡片样式函数
            def draw_indicator_card(x, y, width, height, title, icon, value, status,
                                  bg_color='#ffffff', border_color='#e0e0e0', text_color='#333333', detail_info=''):
                from matplotlib.patches import FancyBboxPatch

                # 绘制卡片背景
                card = FancyBboxPatch((x, y), width, height,
                                    boxstyle="round,pad=0.1",
                                    facecolor=bg_color,
                                    edgecolor=border_color,
                                    linewidth=2,
                                    alpha=0.9)
                ax.add_patch(card)

                # 标题和图标
                ax.text(x + width/2, y + height - 0.25, f'{icon} {title}',
                       fontsize=14, fontweight='bold', ha='center', va='center', color='#555555')

                # 主要数值
                ax.text(x + width/2, y + height/2 + 0.1, value,
                       fontsize=12, fontweight='bold', ha='center', va='center', color=text_color)

                # 真实指标值（灰色小字）
                if detail_info:
                    ax.text(x + width/2, y + height/2 - 0.4, detail_info,
                           fontsize=10, ha='center', va='center', color='#888888', style='italic')

                # 状态标识（通过颜色显示，不显示文字）
                if status:  # 只有当status不为空时才显示
                    ax.text(x + width/2, y + 0.2, f'({status})',
                           fontsize=8, ha='center', va='center', color=text_color)

            # 指标1：积压时段准确性 (左上)
            period_match = indicators['period_accuracy']
            actual_period_info = indicators.get('actual_period_info', '无积压')
            predicted_period_info = indicators.get('predicted_period_info', '无积压')
            period_bg = '#f0fff0' if period_match else '#fff0f0'
            period_border = '#51cf66' if period_match else '#ff6b6b'
            period_text = '#2b8a3e' if period_match else '#e03131'

            draw_indicator_card(1, 6.5, 3.5, 2, '积压时段准确性', '●',
                              f'预测：{predicted_period_info}',
                              '', period_bg, period_border, period_text, f'实际：{actual_period_info}')

            # 指标2：持续时长一致性 (右上)
            duration_match = indicators['duration_consistency']
            actual_duration = indicators.get('actual_duration', 0)
            predicted_duration = indicators.get('predicted_duration', 0)
            duration_bg = '#f0fff0' if duration_match else '#fff0f0'
            duration_border = '#51cf66' if duration_match else '#ff6b6b'
            duration_text = '#2b8a3e' if duration_match else '#e03131'

            draw_indicator_card(5.5, 6.5, 3.5, 2, '持续时长一致性', '■',
                              f'预测：{predicted_duration}小时',
                              '', duration_bg, duration_border, duration_text, f'实际：{actual_duration}小时')

            # 指标3：积压峰值准确性 (左下)
            peak_match = indicators['peak_accuracy']
            actual_peak_value = indicators.get('actual_peak_value', '0班')
            predicted_peak_value = indicators.get('predicted_peak_value', '0班')
            peak_bg = '#f0fff0' if peak_match else '#fff0f0'
            peak_border = '#51cf66' if peak_match else '#ff6b6b'
            peak_text = '#2b8a3e' if peak_match else '#e03131'

            draw_indicator_card(1, 4, 3.5, 2, '积压峰值准确性', '▲',
                              f'预测：{predicted_peak_value}',
                              '', peak_bg, peak_border, peak_text, f'实际：{actual_peak_value}')

            # 指标4：最晚运行时段 (右下)
            latest_match = indicators['latest_period_match']
            actual_latest_period = indicators.get('actual_latest_period', '无积压')
            predicted_latest_period = indicators.get('predicted_latest_period', '无积压')
            latest_bg = '#f0fff0' if latest_match else '#fff0f0'
            latest_border = '#51cf66' if latest_match else '#ff6b6b'
            latest_text = '#2b8a3e' if latest_match else '#e03131'

            draw_indicator_card(5.5, 4, 3.5, 2, '最晚运行时段', '◆',
                              f'预测：{predicted_latest_period}',
                              '', latest_bg, latest_border, latest_text, f'实际：{actual_latest_period}')

            # 总体评估区域
            all_pass = all([period_match, duration_match, peak_match, latest_match])
            overall_status = '达标' if all_pass else '不达标'

            overall_colors = {
                '达标': ('#e8f5e8', '#51cf66', '#2b8a3e'),
                '不达标': ('#ffebee', '#ef5350', '#d32f2f')
            }

            bg_color, border_color, text_color = overall_colors[overall_status]

            # 总体评估卡片
            from matplotlib.patches import FancyBboxPatch
            eval_card = FancyBboxPatch((1, 1.5), 8, 1.8,
                                     boxstyle="round,pad=0.15",
                                     facecolor=bg_color,
                                     edgecolor=border_color,
                                     linewidth=3,
                                     alpha=0.9)
            ax.add_patch(eval_card)

            ax.text(5, 2.8, '总体评估', fontsize=14, fontweight='bold',
                    ha='center', va='center', color='#555555')
            ax.text(5, 2.0, overall_status, fontsize=24, fontweight='bold',
                    ha='center', va='center', color=text_color)

            # 指标说明
            ax.text(5, 0.8, '要求：四个指标全部符合才算达标',
                    fontsize=10, ha='center', style='italic', color='#868e96')

            # 调整布局
            self.figure.tight_layout()

            # 刷新画布
            self.canvas.draw()

        except Exception as e:
            # 错误处理
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, f'积压分析错误: {str(e)}',
                   horizontalalignment='center', verticalalignment='center',
                   transform=ax.transAxes, fontsize=12, color='red')
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
            self.canvas.draw()

    def _calculate_indicators(self, simulation_results):
        """计算四个关键指标"""
        try:
            # 提取实际积压和预测积压的时段
            actual_backlog_periods = []
            predicted_backlog_periods = []

            for _, row in simulation_results.iterrows():
                period_hour = int(row['时段'].split(':')[0])
                actual_backlog = row['实际积压量']
                predicted_backlog = row['预测积压量']

                # 积压定义：延误航班量超过10班
                if actual_backlog > 10:
                    actual_backlog_periods.append(period_hour)
                if predicted_backlog > 10:
                    predicted_backlog_periods.append(period_hour)

            # 指标1：积压时段准确性（偏差不超过1个时段）
            period_accuracy, actual_period_info, predicted_period_info = self._check_period_accuracy_with_value(actual_backlog_periods, predicted_backlog_periods)

            # 指标2：持续时长一致性
            duration_consistency, actual_duration, predicted_duration = self._check_duration_consistency_with_value(actual_backlog_periods, predicted_backlog_periods)

            # 指标3：积压峰值准确性（偏差不超过15%）
            peak_accuracy, actual_peak_value, predicted_peak_value = self._check_peak_accuracy(simulation_results)

            # 指标4：最晚运行时段一致性（使用数据框计算）
            actual_latest_period_raw = self._find_latest_period_from_dataframe(simulation_results, '实际积压量')
            predicted_latest_period_raw = self._find_latest_period_from_dataframe(simulation_results, '预测积压量')

            if actual_latest_period_raw is None and predicted_latest_period_raw is None:
                latest_period_match = True
                actual_latest_period = "无积压"
                predicted_latest_period = "无积压"
            elif actual_latest_period_raw is None:
                latest_period_match = False
                actual_latest_period = "无积压"
                predicted_latest_period = predicted_latest_period_raw
            elif predicted_latest_period_raw is None:
                latest_period_match = False
                actual_latest_period = actual_latest_period_raw
                predicted_latest_period = "无积压"
            else:
                latest_period_match = actual_latest_period_raw == predicted_latest_period_raw
                actual_latest_period = actual_latest_period_raw
                predicted_latest_period = predicted_latest_period_raw

            return {
                'period_accuracy': period_accuracy,
                'actual_period_info': actual_period_info,
                'predicted_period_info': predicted_period_info,
                'duration_consistency': duration_consistency,
                'actual_duration': actual_duration,
                'predicted_duration': predicted_duration,
                'peak_accuracy': peak_accuracy,
                'actual_peak_value': actual_peak_value,
                'predicted_peak_value': predicted_peak_value,
                'latest_period_match': latest_period_match,
                'actual_latest_period': actual_latest_period,
                'predicted_latest_period': predicted_latest_period
            }

        except Exception:
            # 如果计算出错，返回默认值
            return {
                'period_accuracy': False,
                'actual_period_info': '计算错误',
                'predicted_period_info': '计算错误',
                'duration_consistency': False,
                'actual_duration': 0,
                'predicted_duration': 0,
                'peak_accuracy': False,
                'actual_peak_value': 0,
                'predicted_peak_value': 0,
                'latest_period_match': False,
                'actual_latest_period': 0,
                'predicted_latest_period': 0
            }

    def _check_period_accuracy_with_value(self, actual_periods, predicted_periods):
        """
        指标1：推演的积压时段准确性
        规则：与实际积压时段的开始和结束时间相比，前后偏差不超过1个时段。
        积压时段定义：延误航班量首次超过10班到最后一次降到10班以下的时段
        """
        # 获取实际和预测的积压时段范围
        actual_start, actual_end = self._get_backlog_period_range(actual_periods)
        predicted_start, predicted_end = self._get_backlog_period_range(predicted_periods)

        if actual_start is None and predicted_start is None:
            return True, "无积压", "无积压"
        if actual_start is None:
            predicted_info = self._format_time_period(predicted_start, predicted_end)
            return False, "无积压", predicted_info
        if predicted_start is None:
            actual_info = self._format_time_period(actual_start, actual_end)
            return False, actual_info, "无积压"

        # 计算起始时段偏差
        start_diff = abs(actual_start - predicted_start)
        # 计算结束时段偏差
        end_diff = abs(actual_end - predicted_end)

        # 检查起始和结束时段的偏差是否都在1小时内
        accuracy = start_diff <= 1 and end_diff <= 1

        # 返回实际和预测信息（格式化为时段）
        actual_info = self._format_time_period(actual_start, actual_end)
        predicted_info = self._format_time_period(predicted_start, predicted_end)
        return accuracy, actual_info, predicted_info

    def _get_backlog_period_range(self, periods):
        """获取积压时段范围（基于超过10班的定义）"""
        if not periods:
            return None, None
        return min(periods), max(periods)

    def _format_time_period(self, start_hour, end_hour):
        """格式化连续时段显示为 HH:00-HH:59 格式"""
        if start_hour is None or end_hour is None:
            return "无积压"
        return f"{start_hour:02d}:00-{end_hour:02d}:59"

    def _format_single_time_period(self, hour):
        """格式化单个时段显示为 HH:00-HH:59 格式"""
        if hour is None:
            return "无积压"
        return f"{hour:02d}:00-{hour:02d}:59"

    def _calculate_duration_hours(self, start_hour, end_hour):
        """计算时段持续小时数"""
        if start_hour is None or end_hour is None:
            return 0
        return end_hour - start_hour + 1

    def _check_duration_consistency_with_value(self, actual_periods, predicted_periods):
        """
        指标2：推演的积压时段持续时长
        规则：持续时长与实际一致。
        """
        # 计算实际持续时长（基于时段范围）
        if actual_periods:
            actual_start, actual_end = self._get_backlog_period_range(actual_periods)
            actual_duration = self._calculate_duration_hours(actual_start, actual_end)
        else:
            actual_duration = 0

        # 计算预测持续时长（基于时段范围）
        if predicted_periods:
            predicted_start, predicted_end = self._get_backlog_period_range(predicted_periods)
            predicted_duration = self._calculate_duration_hours(predicted_start, predicted_end)
        else:
            predicted_duration = 0

        # 比较实际与预测的持续小时数是否相等
        consistency = actual_duration == predicted_duration

        # 返回一致性以及实际和预测持续时长
        return consistency, actual_duration, predicted_duration

    def _check_peak_accuracy(self, simulation_results):
        """
        指标3：积压最高峰准确性
        规则：最高峰发生的时段要与实际一致，且积压量与实际偏差不高于15%。
        积压峰值定义：预测积压量+计划离港数的最大值
        """
        try:
            # 计算实际峰值（实际积压量+计划离岗数）
            simulation_results['实际总量'] = simulation_results['实际积压量'] + simulation_results['计划离岗数']
            # 计算预测峰值（预测积压量+计划离岗数）
            simulation_results['预测总量'] = simulation_results['预测积压量'] + simulation_results['计划离岗数']

            # 只考虑积压量大于10的时段
            actual_backlog_data = simulation_results[simulation_results['实际积压量'] > 10]
            predicted_backlog_data = simulation_results[simulation_results['预测积压量'] > 10]

            # 处理没有积压的情况
            if len(actual_backlog_data) == 0 and len(predicted_backlog_data) == 0:
                return True, "0班", "0班"
            if len(actual_backlog_data) == 0:
                predicted_peak_idx = predicted_backlog_data['预测总量'].idxmax()
                predicted_peak_value = predicted_backlog_data.loc[predicted_peak_idx, '预测总量']
                predicted_peak_period = predicted_backlog_data.loc[predicted_peak_idx, '时段']
                return False, "0班", f"{int(predicted_peak_value)}班({predicted_peak_period})"
            if len(predicted_backlog_data) == 0:
                actual_peak_idx = actual_backlog_data['实际总量'].idxmax()
                actual_peak_value = actual_backlog_data.loc[actual_peak_idx, '实际总量']
                actual_peak_period = actual_backlog_data.loc[actual_peak_idx, '时段']
                return False, f"{int(actual_peak_value)}班({actual_peak_period})", "0班"

            # 找到实际积压的最高峰
            actual_peak_idx = actual_backlog_data['实际总量'].idxmax()
            actual_peak_value = actual_backlog_data.loc[actual_peak_idx, '实际总量']
            actual_peak_period = actual_backlog_data.loc[actual_peak_idx, '时段']

            # 找到预测积压的最高峰
            predicted_peak_idx = predicted_backlog_data['预测总量'].idxmax()
            predicted_peak_value = predicted_backlog_data.loc[predicted_peak_idx, '预测总量']
            predicted_peak_period = predicted_backlog_data.loc[predicted_peak_idx, '时段']

            # 规则1：检查峰值发生的时段是否一致
            period_match = actual_peak_period == predicted_peak_period

            # 规则2：计算峰值积压量的偏差
            if actual_peak_value > 0:
                deviation = abs(predicted_peak_value - actual_peak_value) / actual_peak_value * 100
            else:
                deviation = 0 if predicted_peak_value == 0 else 100

            # 最终准确性：时段一致 且 偏差不高于15%
            peak_accuracy = period_match and deviation <= 15

            # 返回带时段信息的峰值
            actual_peak_info = f"{int(actual_peak_value)}班({actual_peak_period})"
            predicted_peak_info = f"{int(predicted_peak_value)}班({predicted_peak_period})"

            return peak_accuracy, actual_peak_info, predicted_peak_info

        except Exception:
            return False, "0班", "0班"

    def _find_latest_period_from_dataframe(self, simulation_results, column_name):
        """从数据框中找到最晚运行时段（从最后一行往前找第一个非0的积压量）"""
        for i in range(len(simulation_results) - 1, -1, -1):
            if simulation_results.iloc[i][column_name] > 0:
                return simulation_results.iloc[i]['时段']
        return None

    def _check_latest_period_with_value(self, actual_periods, predicted_periods):
        """
        指标4：推演的航班最晚运行时段
        规则：与实际的最晚运行时段要一致。
        最晚运行时段定义：从数据框最后一行往前找第一个非0的积压量对应的时段
        """
        # 这个方法需要在调用时传入simulation_results，暂时保持原逻辑
        if not actual_periods and not predicted_periods:
            return True, "无积压", "无积压"

        # 获取实际最晚运行时段（单个时段格式）
        if actual_periods:
            actual_latest = max(actual_periods)
            actual_latest_info = self._format_single_time_period(actual_latest)
        else:
            actual_latest = 0
            actual_latest_info = "无积压"

        # 获取预测最晚运行时段（单个时段格式）
        if predicted_periods:
            predicted_latest = max(predicted_periods)
            predicted_latest_info = self._format_single_time_period(predicted_latest)
        else:
            predicted_latest = 0
            predicted_latest_info = "无积压"

        # 检查两者是否一致
        match = actual_latest == predicted_latest

        # 返回一致性以及实际和预测最晚时段（格式化）
        return match, actual_latest_info, predicted_latest_info

    def clear_chart(self):
        """清空图表"""
        self.figure.clear()
        self.canvas.draw()


class ReschedulingChartWidget(QWidget):
    """重排班优化效果图表组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)

        # 创建matplotlib图形
        self.figure = Figure(figsize=(12, 10))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)

        # 初始化显示
        self.show_empty_chart()

    def show_empty_chart(self):
        """显示空图表"""
        self.figure.clear()
        ax = self.figure.add_subplot(1, 1, 1)
        ax.text(0.5, 0.5, '请先进行重排班优化',
                ha='center', va='center', fontsize=16,
                transform=ax.transAxes, color='gray')
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
        self.canvas.draw()

    def update_charts(self, original_df, optimized_df):
        """更新重排班优化效果图表"""
        if optimized_df is None or optimized_df.empty:
            self.show_empty_chart()
            return

        try:
            # 清除之前的图表
            self.figure.clear()

            # 检查是否有乘客数据
            has_passenger_data = 'passenger_count' in optimized_df.columns and optimized_df['passenger_count'].sum() > 0

            # 预处理数据（按照predict.py的逻辑）
            processed_df = self._preprocess_data(optimized_df, has_passenger_data)

            if has_passenger_data:
                # 有乘客数据时显示4个图表
                gs = self.figure.add_gridspec(2, 2, hspace=0.3, wspace=0.3)

                # 图1: 时隙分布对比
                ax1 = self.figure.add_subplot(gs[0, 0])
                self._plot_slot_distribution(ax1, processed_df)

                # 图2: 延误改善对比
                ax2 = self.figure.add_subplot(gs[0, 1])
                self._plot_delay_improvement(ax2, processed_df)

                # 图3: 改善率统计
                ax3 = self.figure.add_subplot(gs[1, 0])
                self._plot_improvement_rate(ax3, processed_df)

                # 图4: 延误成本对比
                ax4 = self.figure.add_subplot(gs[1, 1])
                self._plot_cost_comparison(ax4, processed_df)
            else:
                # 没有乘客数据时显示2个图表
                gs = self.figure.add_gridspec(1, 2, hspace=0.3, wspace=0.3)

                # 图1: 时隙分布对比
                ax1 = self.figure.add_subplot(gs[0, 0])
                self._plot_slot_distribution(ax1, processed_df)

                # 图2: 延误改善对比
                ax2 = self.figure.add_subplot(gs[0, 1])
                self._plot_delay_improvement(ax2, processed_df)

        except Exception as e:
            # 如果出现错误，显示错误信息
            self.figure.clear()
            ax = self.figure.add_subplot(1, 1, 1)
            ax.text(0.5, 0.5, f'图表生成失败\n{str(e)}',
                    ha='center', va='center', fontsize=14,
                    transform=ax.transAxes, color='red')
            ax.axis('off')

        self.figure.tight_layout()
        self.canvas.draw()

    def _preprocess_data(self, df, has_passenger_data):
        """预处理数据（按照predict.py的逻辑）"""
        # 确保必要的列存在
        if 'expected_departure' not in df.columns:
            df['expected_departure'] = df['actual_departure']

        # 添加时隙计数
        if 'optimized_departure' in df.columns:
            df['slot_count'] = df.groupby('optimized_departure')['optimized_departure'].transform('count')
        df['origin_slot_count'] = df.groupby('scheduled_departure')['scheduled_departure'].transform('count')

        # 时间转换为分钟数
        def time_to_minutes(ts):
            if pd.isna(ts):
                return 0
            dt = pd.to_datetime(ts)
            return dt.hour * 60 + dt.minute + dt.second / 60.0

        for col in ['scheduled_departure', 'actual_departure', 'expected_departure']:
            if col in df.columns:
                df[col + '_min'] = df[col].apply(time_to_minutes)

        if 'optimized_departure' in df.columns:
            df['optimized_departure_min'] = df['optimized_departure'].apply(time_to_minutes)

        # 计算延误
        df['origin_delay'] = (df['expected_departure_min'] - df['scheduled_departure_min'] - 15).clip(lower=0)
        if 'optimized_departure_min' in df.columns:
            df['new_delay'] = (df['expected_departure_min'] - df['optimized_departure_min'] - 15).clip(lower=0)
            df['delta_delay'] = df['origin_delay'] - df['new_delay']

        # 计算成本（如果有乘客数据）
        if has_passenger_data:
            def delay_cost_calculate(delay, passenger_count):
                if delay < 240:
                    return 0
                elif delay <= 480:
                    return 200 * passenger_count
                else:
                    return 400 * passenger_count

            df['origin_delay_cost'] = df.apply(lambda x: delay_cost_calculate(x['origin_delay'], x.get('passenger_count', 0)), axis=1)
            if 'new_delay' in df.columns:
                df['new_delay_cost'] = df.apply(lambda x: delay_cost_calculate(x['new_delay'], x.get('passenger_count', 0)), axis=1)
                df['delta_cost'] = df['origin_delay_cost'] - df['new_delay_cost']

        return df

    def _plot_slot_distribution(self, ax, df):
        """绘制时隙分布对比图（按照predict.py逻辑）"""
        ax.set_title('时隙分布对比', fontsize=12, fontweight='bold')

        # 创建5分钟时隙的直方图
        bins = np.arange(0, 24*60+1, 5)

        # 原始时隙分布
        orig_min_day = (pd.to_datetime(df['scheduled_departure']) - pd.to_datetime(df['scheduled_departure']).dt.normalize()).dt.total_seconds() / 60

        # 优化后时隙分布
        if 'optimized_departure' in df.columns:
            new_min_day = (pd.to_datetime(df['optimized_departure']) - pd.to_datetime(df['optimized_departure']).dt.normalize()).dt.total_seconds() / 60
            ax.hist(new_min_day, bins=bins, alpha=0.5, label='优化后', color='orange')

        ax.hist(orig_min_day, bins=bins, alpha=0.5, label='优化前', color='steelblue')

        ax.set_xlabel('一天中的时间 (从00:00开始的分钟数)')
        ax.set_ylabel('航班数量')
        ax.legend()
        ax.grid(True, alpha=0.3)

    def _plot_delay_improvement(self, ax, df):
        """绘制延误改善对比图"""
        ax.set_title('每航班延误改善', fontsize=12, fontweight='bold')

        if 'delta_delay' in df.columns:
            x = np.arange(len(df))
            ax.bar(x, df['delta_delay'], color='seagreen', alpha=0.7)
            ax.set_xlabel('航班索引')
            ax.set_ylabel('延误减少 (分钟)')
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, '无延误改善数据', ha='center', va='center', transform=ax.transAxes)

    def _plot_improvement_rate(self, ax, df):
        """绘制总体改善率"""
        ax.set_title('总体延误改善率', fontsize=12, fontweight='bold')

        if 'origin_delay' in df.columns and 'new_delay' in df.columns:
            total_origin_delay = df['origin_delay'].sum()
            total_new_delay = df['new_delay'].sum()

            improve_rate = ((total_origin_delay - total_new_delay) / total_origin_delay) * 100 if total_origin_delay > 0 else 0

            ax.bar(['延误改善率'], [improve_rate], color='seagreen', alpha=0.7)
            ax.set_ylabel('改善率 (%)')
            ax.text(0, improve_rate + 1, f'{improve_rate:.1f}%', ha='center', va='bottom')
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, '无改善率数据', ha='center', va='center', transform=ax.transAxes)

    def _plot_cost_comparison(self, ax, df):
        """绘制延误成本对比"""
        ax.set_title('延误成本对比', fontsize=12, fontweight='bold')

        if 'new_delay_cost' in df.columns and 'origin_delay_cost' in df.columns:
            x = np.arange(len(df))
            ax.plot(x, df['origin_delay_cost'], label='优化前成本', color='red', alpha=0.7)
            ax.plot(x, df['new_delay_cost'], label='优化后成本', color='blue', alpha=0.7)
            ax.set_xlabel('航班索引')
            ax.set_ylabel('成本')
            ax.legend()
            ax.grid(True, alpha=0.3)
        else:
            ax.text(0.5, 0.5, '无成本数据', ha='center', va='center', transform=ax.transAxes)
