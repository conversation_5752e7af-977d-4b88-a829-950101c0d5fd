#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立预测脚本 - predict.py
功能：基于训练好的PPO模型进行航班延误预测和重排班
依赖：只需要pt权重文件和输入Excel文件
"""

import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings("ignore", category=FutureWarning)

import torch
import torch.nn as nn
from openpyxl import load_workbook
from openpyxl.drawing.image import Image as XLImage
import seaborn as sns

# ============= 数据读取类 =============
class FlightDataLoader:
    def __init__(self, excel_path, mode='test'):
        self.excel_path = excel_path
        self.mode = mode
        self.data = self.load_data()

    def load_data(self):
        df = pd.read_excel(self.excel_path)
        df['scheduled_departure'] = pd.to_datetime(df['scheduled_departure'])
        if 'actual_departure' in df.columns:
            df['actual_departure'] = pd.to_datetime(df['actual_departure'])
        if 'expected_departure' in df.columns:
            df['expected_departure'] = pd.to_datetime(df['expected_departure'])
        if 'passenger_count_max' not in df.columns:
            df['passenger_count_max'] = 0
        if 'passenger_count' not in df.columns:
            df['passenger_count'] = 0
        if 'arrival_place' not in df.columns:
            df['arrival_place'] = ""
        cols_needed = ['flight_id', 'scheduled_departure', 'actual_departure', 'expected_departure', 
                      'passenger_count', 'passenger_count_max', 'arrival_place']
        cols = [col for col in cols_needed if col in df.columns]
        df = df[cols]
        return df

    def get_data(self):
        return self.data

# ============= 时隙管理函数 =============
def generate_time_slots(window_start, window_end, slot_length=5):
    slots = []
    t = window_start
    while t < window_end:
        slot_end = t + timedelta(minutes=slot_length)
        slots.append({'slot_start': t, 'slot_end': slot_end})
        t = slot_end
    return slots

def get_slot_index_for_time(slots, dt):
    for idx, slot in enumerate(slots):
        if slot['slot_start'] <= dt < slot['slot_end']:
            return idx
    return None

def filter_available_slots(df, window_start, window_end, slot_length=5):
    slots = generate_time_slots(window_start, window_end, slot_length)
    available = [False] * len(slots)
    for idx, row in df.iterrows():
        sch_dep = row['scheduled_departure']
        slot_idx = get_slot_index_for_time(slots, sch_dep)
        if slot_idx is not None:
            available[slot_idx] = True
    return available

def build_global_slot_index(slots_per_window):
    slot_key2idx = dict()
    idx2slot = dict()
    idx = 0
    for slots in slots_per_window:
        for slot in slots:
            key = (slot['slot_start'], slot['slot_end'])
            if key not in slot_key2idx:
                slot_key2idx[key] = idx
                idx2slot[idx] = slot
                idx += 1
    return slot_key2idx, idx2slot

def delay_cost_caculate(delay, passenger_count):
    return np.where(delay < 240, 0,
                    np.where(delay <= 480, 200 * passenger_count,
                             400 * passenger_count)).astype(int)

# ============= 环境类 =============
class FlightRecoveryEnv:
    def __init__(
        self, flights_df, slots,
        available_slots, slot_length=5, max_per_slot=5,
        global_slot_key2idx=None, global_slot_counts=None,
        slot_reward_pos=1.0, slot_reward_neg=-0.7,
        cancel_penalty=-2000,
        allow_cancel=True, 
        use_expected_depature=False   
    ):
        self.flights_df = flights_df.reset_index(drop=True)
        self.slots = slots
        self.available_slots = available_slots
        self.slot_length = slot_length
        self.max_per_slot = max_per_slot

        self.slot_counts = [0 for _ in self.slots]
        self.flight_assignments = [-1 for _ in range(len(self.flights_df))]
        self.state = (tuple(self.flight_assignments), tuple(self.slot_counts))
        self.done = False

        self.global_slot_key2idx = global_slot_key2idx
        self.global_slot_counts = global_slot_counts

        self.use_expected_depature = use_expected_depature

        self.original_delays = []
        for idx, flight in self.flights_df.iterrows():
            sch_dep = flight['scheduled_departure']
            if self.use_expected_depature and 'expected_departure' in flight:
                actual_dep = flight['expected_departure']
            else:
                actual_dep = flight['actual_departure']
            delay = (actual_dep - sch_dep).total_seconds() / 60.0 - 15
            self.original_delays.append(max(delay, 0))

        self.slot_reward_pos = slot_reward_pos
        self.slot_reward_neg = slot_reward_neg
        self.cancel_penalty = cancel_penalty
        self.allow_cancel = allow_cancel  

    def get_action_mask(self):
        n_flight = len(self.flights_df)
        n_slot = len(self.slots)
        mask = np.zeros((n_flight, n_slot + 1), dtype=np.bool_)  
        for idx, assigned in enumerate(self.flight_assignments):
            if assigned == -1:
                flight = self.flights_df.iloc[idx]
                sch_dep = flight['scheduled_departure']
                if self.use_expected_depature and 'expected_departure' in flight:
                    actual_dep = flight['expected_departure']
                else:
                    actual_dep = flight['actual_departure']
                for slot_idx, slot in enumerate(self.slots):
                    if slot['slot_start'] > actual_dep  + timedelta(minutes=20):
                        continue
                    if not self.available_slots[slot_idx]:
                        continue
                    if self.get_slot_remain_capacity(slot_idx) <= 0:
                        continue
                    if slot['slot_start'] < sch_dep-timedelta(minutes=10):
                        continue
                    mask[idx, slot_idx] = True
                if self.allow_cancel:
                    mask[idx, n_slot] = True
                else:
                    mask[idx, n_slot] = False
        return mask

    def get_slot_remain_capacity(self, slot_idx):
        cap_local = self.slot_counts[slot_idx]
        if (self.global_slot_key2idx is not None) and (self.global_slot_counts is not None):
            key = (self.slots[slot_idx]['slot_start'], self.slots[slot_idx]['slot_end'])
            global_idx = self.global_slot_key2idx.get(key, None)
            cap_global = self.global_slot_counts[global_idx] if global_idx is not None else 0
            return self.max_per_slot - (cap_local + cap_global)
        else:
            return self.max_per_slot - cap_local

    def step(self, action):
        flight_idx, slot_idx = action
        if self.flight_assignments[flight_idx] != -1:
            raise Exception(f"航班{flight_idx}已分配，不可重复分配")
        n_slot = len(self.slots)
        flight = self.flights_df.iloc[flight_idx]
        sch_dep = flight['scheduled_departure']
        if self.use_expected_depature and 'expected_departure' in flight:
            actual_dep = flight['expected_departure']
        else:
            actual_dep = flight['actual_departure']
        passenger_count = flight['passenger_count']
        original_delay = self.original_delays[flight_idx]

        # 取消动作
        if slot_idx == n_slot:
            self.flight_assignments[flight_idx] = -2  # -2表示取消
            reward = self.cancel_penalty
            done = all([x != -1 for x in self.flight_assignments])
            self.done = done
            self.state = (tuple(self.flight_assignments), tuple(self.slot_counts))
            return self.state, reward, done

        prev_slot_count = self.slot_counts[slot_idx]
        self.flight_assignments[flight_idx] = slot_idx
        self.slot_counts[slot_idx] += 1
        new_sch_departure = self.slots[slot_idx]['slot_start']
        if (self.global_slot_key2idx is not None) and (self.global_slot_counts is not None):
            key = (self.slots[slot_idx]['slot_start'], self.slots[slot_idx]['slot_end'])
            global_idx = self.global_slot_key2idx.get(key, None)
            if global_idx is not None:
                self.global_slot_counts[global_idx] += 1

        delay = (actual_dep - new_sch_departure).total_seconds() / 60.0 -15
        delay = max(delay, 0)

        reward_delay = -(delay - original_delay) / 100.0

        if delay < 240:
            delay_cost = 0
        elif delay <= 480 :
            delay_cost = int(200 * passenger_count)
        else:
            delay_cost = int(400 * passenger_count)
        if original_delay < 240:
            original_delay_cost = 0
        elif original_delay <= 480 :
            original_delay_cost = int(200 * passenger_count)
        else:
            original_delay_cost = int(400 * passenger_count)

        reward_cost = -(delay_cost - original_delay_cost) / 2000.0

        slot_use_reward = 0
        if prev_slot_count == 0:
            slot_use_reward = self.slot_reward_pos
        elif prev_slot_count in [1, 2, 3, 4]:
            slot_use_reward = self.slot_reward_neg

        reward = reward_delay + reward_cost + slot_use_reward

        done = all([x != -1 for x in self.flight_assignments])
        self.done = done
        self.state = (tuple(self.flight_assignments), tuple(self.slot_counts))
        return self.state, reward, done

    def reset(self):
        self.slot_counts = [0 for _ in self.slots]
        self.flight_assignments = [-1 for _ in range(len(self.flights_df))]
        self.state = (tuple(self.flight_assignments), tuple(self.slot_counts))
        self.done = False
        return self.state

    def get_assignment_results(self, use_actual_for_stats=False):
        results = []
        n_slot = len(self.slots)
        for idx, slot_idx in enumerate(self.flight_assignments):
            flight = self.flights_df.iloc[idx]
            result = {
                'flight_id': flight['flight_id'],
                'scheduled_departure': flight['scheduled_departure'],
                'actual_departure': flight['actual_departure'] if 'actual_departure' in flight else None,
                'expected_departure': flight['expected_departure'] if 'expected_departure' in flight else None,
                'passenger_count': flight['passenger_count']
            }
            sch_dep = flight['scheduled_departure']
            if use_actual_for_stats and 'actual_departure' in flight and 'expected_departure' in flight:
                actual_dep = flight['actual_departure']
                expected_dep = flight['expected_departure']
            elif 'expected_departure' in flight:
                actual_dep = flight['expected_departure']
                expected_dep = flight['expected_departure']
            else:
                actual_dep = flight['actual_departure']
                expected_dep = flight.get('expected_departure', None) or flight['actual_departure']

            if slot_idx == -1:
                result['new_scheduled_departure'] = sch_dep - timedelta(minutes=5)
                result['new_delay_minutes'] = max(0,(expected_dep - sch_dep).total_seconds() / 60.0 -15)
                result['new_delay_cost'] = result['new_delay_minutes'] * flight['passenger_count']
                result['cancelled'] = False
                result['origin_delay_minutes'] = max(0,(expected_dep - sch_dep).total_seconds() / 60.0 -15)
                result['origin_delay_cost'] = delay_cost_caculate(result['origin_delay_minutes'],flight['passenger_count'])
                # 真实
                result['ture_new_delay'] = max(0,(actual_dep - sch_dep).total_seconds() / 60.0 -15)
                result['ture_new_cost'] = result['ture_new_delay'] * flight['passenger_count']
                result['ture_origin_delay'] = max(0,(actual_dep - sch_dep).total_seconds() / 60.0 -15)
                result['ture_origin_cost'] = delay_cost_caculate(result['ture_origin_delay'] , flight['passenger_count'])
            elif slot_idx == n_slot or slot_idx == -2:
                result['new_scheduled_departure'] = None
                result['new_delay_minutes'] = 9999
                result['new_delay_cost'] = 9999 * flight['passenger_count']
                result['cancelled'] = True
                result['origin_delay_minutes'] = max(0,(expected_dep - sch_dep).total_seconds() / 60.0 -15)
                result['origin_delay_cost'] = result['origin_delay_minutes'] * flight['passenger_count']
                result['ture_new_delay'] = 9999
                result['ture_new_cost'] = 9999 * flight['passenger_count']
                result['ture_origin_delay'] = max(0,(actual_dep - sch_dep).total_seconds() / 60.0 -15)
                result['ture_origin_cost'] = delay_cost_caculate(result['ture_origin_delay'] , flight['passenger_count'])
            else:
                new_sch_dep = self.slots[slot_idx]['slot_start']
                delay = (expected_dep - new_sch_dep).total_seconds() / 60.0 -15
                delay = max(delay, 0)
                result['new_scheduled_departure'] = new_sch_dep
                result['new_delay_minutes'] = delay
                result['new_delay_cost'] = delay_cost_caculate(delay , flight['passenger_count'])
                result['cancelled'] = False
                result['origin_delay_minutes'] = max(0,(expected_dep - sch_dep).total_seconds() / 60.0 -15)
                result['origin_delay_cost'] = delay_cost_caculate(result['origin_delay_minutes'] , flight['passenger_count'])
                # 真实
                delay_true = (actual_dep - new_sch_dep).total_seconds() / 60.0 -15
                delay_true = max(delay_true, 0)
                result['ture_new_delay'] = delay_true
                result['ture_new_cost'] = delay_cost_caculate(delay_true , flight['passenger_count'])
                result['ture_origin_delay'] = max(0,(actual_dep - sch_dep).total_seconds() / 60.0 -15)
                result['ture_origin_cost'] = delay_cost_caculate(result['ture_origin_delay'] , flight['passenger_count'])
            results.append(result)
        return pd.DataFrame(results)

# ============= 神经网络模型 =============
def flatten_state(state):
    return np.array(state[0] + state[1], dtype=np.float32)

class MaskedActorCritic(nn.Module):
    def __init__(self, state_dim, action_dim, hidden_dim=128):
        super(MaskedActorCritic, self).__init__()
        self.fc1 = nn.Linear(state_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.actor_head = nn.Linear(hidden_dim, action_dim)
        self.critic_head = nn.Linear(hidden_dim, 1)

    def forward(self, x):
        x = torch.relu(self.fc1(x))
        x = torch.relu(self.fc2(x))
        logits = self.actor_head(x)
        value = self.critic_head(x)
        return logits, value

    def get_action(self, state, action_mask, device='cpu', temperature=2.0):
        state_tensor = torch.tensor(flatten_state(state), dtype=torch.float32).unsqueeze(0).to(device)
        logits, value = self.forward(state_tensor)
        mask_tensor = torch.tensor(action_mask.flatten(), dtype=torch.bool).to(device)
        logits = logits.masked_fill(~mask_tensor, float('-inf'))
        probs = torch.softmax(logits / temperature, dim=-1)
        dist = torch.distributions.Categorical(probs)
        action_idx = dist.sample().item()
        log_prob = dist.log_prob(torch.tensor(action_idx).to(device)).item()
        return action_idx, log_prob, value.item(), probs.cpu().detach().numpy()

def decode_action(action_idx, n_flight, n_slot):
    slot_plus = n_slot + 1
    flight_idx = action_idx // slot_plus
    slot_idx = action_idx % slot_plus
    return (flight_idx, slot_idx)

# ============= 航班合并功能 =============
def merge_flights(assignment: pd.DataFrame) -> pd.DataFrame:
    """
    assignment: 排班结果表，必须包含 flight_id、scheduled_departure、new_scheduled_departure、cancelled、
        passenger_count、passenger_count_max、arrival_place 等字段。
    返回：合并后更新过的 assignment
    """
    df = assignment.copy()
    df['merge_note'] = ""  # 用于记录合并情况
    df['merged_to'] = None # 若被并入其他航班，记录目标航班id

    # 只考虑未取消航班，并安全检查列是否存在
    required_cols = ['cancelled', 'passenger_count_max', 'arrival_place']
    if not all(col in df.columns for col in required_cols):
        print("Warning: Required columns for merging not found in assignment DataFrame.")
        return df

    valid_flights = df[(~df['cancelled']) & (df['passenger_count_max'].notna()) & (df['arrival_place'] != "")]
    merged_A = set()

    for idx_a, row_a in valid_flights.iterrows():
        # 条件1：A航班未被合并，载客率<0.5，延误>200min
        passenger_count_max_a = row_a.get('passenger_count_max', 0)
        passenger_count_a = row_a.get('passenger_count', 0)

        if passenger_count_max_a <= 0 or (passenger_count_a / passenger_count_max_a >= 0.5):
            continue
        # 延误计算：new_delay_minutes
        if ('new_delay_minutes' not in row_a) or (row_a['new_delay_minutes'] <= 200):
            continue
        if row_a['flight_id'] in merged_A:
            continue

        # 寻找可合并B航班（目的地相同，且合并后总人数<=B的额定载客量，B不能是A本身）
        candidates = valid_flights[
            (valid_flights['arrival_place'] == row_a['arrival_place']) &
            (valid_flights['flight_id'] != row_a['flight_id']) &
            (~valid_flights['flight_id'].isin(merged_A))
        ].copy() # Create a copy to avoid SettingWithCopyWarning

        for idx_b, row_b in candidates.iterrows():
            passenger_count_max_b = row_b.get('passenger_count_max', 0)
            passenger_count_b = row_b.get('passenger_count', 0)
            total_passengers = passenger_count_a + passenger_count_b

            # B航班额定载客量有效且足够
            if passenger_count_max_b <= 0 or total_passengers > passenger_count_max_b:
                continue
            # 合并！A航班被并入B航班
            # A航班被标记为取消（但无取消损失），延误成本重新计算，merge_note标记
            # B航班不变
            df.loc[idx_a, 'cancelled'] = True
            df.loc[idx_a, 'merge_note'] = f"Merged to {row_b['flight_id']}"
            df.loc[idx_a, 'merged_to'] = row_b['flight_id']
            # 不计取消损失
            scheduled_departure_a = pd.to_datetime(row_a['scheduled_departure'])
            new_scheduled_departure_b = pd.to_datetime(row_b['new_scheduled_departure'])
            if pd.notna(scheduled_departure_a) and pd.notna(new_scheduled_departure_b):
                delay_minutes = (new_scheduled_departure_b - scheduled_departure_a).total_seconds()/60.0 - 15
                df.loc[idx_a, 'new_delay_cost'] = passenger_count_a * max(0, delay_minutes)
                df.loc[idx_a, 'new_delay_minutes'] = max(0, delay_minutes)
            else:
                df.loc[idx_a, 'new_delay_cost'] = 0 # Or other appropriate value
                df.loc[idx_a, 'new_delay_minutes'] = 0 # Or other appropriate value

            # 其他成本字段同步
            for field in ['ture_new_cost', 'ture_new_delay']:
                if (field in df.columns) and ('actual_departure' in row_b):
                    actual_departure_b = pd.to_datetime(row_b['actual_departure'])
                    if pd.notna(scheduled_departure_a) and pd.notna(actual_departure_b):
                        delay_minutes_true = (actual_departure_b - scheduled_departure_a).total_seconds()/60.0 - 15
                        df.loc[idx_a, field] = passenger_count_a * max(0, delay_minutes_true)
                    else:
                        df.loc[idx_a, field] = 0 # Or other appropriate value

            merged_A.add(row_a['flight_id'])
            break # 每个A只合并一次

    return df

# ============= 批处理和填充功能 =============
def pad_flights_to_batch(flights_df, batch_size, slot_count):
    n = len(flights_df)
    if n >= batch_size:
        df = flights_df.iloc[:batch_size].copy()
        real_mask = np.ones(batch_size, dtype=bool)
        return df, real_mask
    else:
        df_real = flights_df.copy()
        virtual_rows = []
        max_sch_dep = df_real['scheduled_departure'].max() if not df_real.empty else datetime.now()

        for i in range(batch_size - n):
            virtual_row = {
                'flight_id': f'virtual_{i}',
                'scheduled_departure': max_sch_dep + timedelta(minutes=5*(i+1)),
                'actual_departure': max_sch_dep + timedelta(minutes=10*(i+1)),
                'expected_departure': max_sch_dep + timedelta(minutes=10*(i+1)),
                'passenger_count': 0,
                'passenger_count_max': 0, # Add default for virtual flights
                'arrival_place': '' # Add default for virtual flights
            }
            virtual_rows.append(virtual_row)
        df_virtual = pd.DataFrame(virtual_rows)
        df = pd.concat([df_real, df_virtual], ignore_index=True)
        real_mask = np.array([True]*n + [False]*(batch_size-n))
        return df, real_mask

def pad_slots_to_count(slots, slot_count, window_end):
    n = len(slots)
    if n >= slot_count:
        return slots, np.ones(slot_count, dtype=bool)
    else:
        virtual_slots = []
        t = slots[-1]['slot_end'] if n > 0 else window_end
        for i in range(slot_count - n):
            slot_start = t + timedelta(minutes=5*i)
            slot_end = slot_start + timedelta(minutes=5)
            virtual_slots.append({'slot_start': slot_start, 'slot_end': slot_end})
        all_slots = slots + virtual_slots
        real_mask = np.array([True]*n + [False]*(slot_count-n))
        return all_slots, real_mask

def get_action_mask_with_virtuals(env, flight_real_mask, slot_real_mask):
    n_flight, n_slot = len(env.flights_df), len(env.slots)
    mask = env.get_action_mask()
    for i, is_real_flight in enumerate(flight_real_mask):
        if not is_real_flight:
            mask[i, :] = False
    for j, is_real_slot in enumerate(slot_real_mask):
        if not is_real_slot:
            mask[:, j] = False
    return mask

# ============= 批处理预测功能 =============
def batch_predict(
    agent, model_path,
    flights_df, slots, available_slots, slot_occupied_counts,
    slot_length, max_per_slot,
    batch_flight_size=46, slot_count=72, device='cpu',
    slot_real_mask=None,
    global_slot_key2idx=None, global_slot_counts=None,
    allow_cancel=True,
    use_expected_depature=False
):
    total_results = []
    n_flight = len(flights_df)
    flight_ptr = 0
    batch_id = 0
    unassigned_flights = []
    all_flight_ids = set(flights_df['flight_id'])
    assigned_flight_ids = set()

    while flight_ptr < n_flight:
        batch_df = flights_df.iloc[flight_ptr:flight_ptr+batch_flight_size]
        batch_df, flight_real_mask = pad_flights_to_batch(batch_df, batch_flight_size, slot_count)
        env = FlightRecoveryEnv(
            batch_df, slots, available_slots, slot_length=slot_length, max_per_slot=max_per_slot,
            global_slot_key2idx=global_slot_key2idx,
            global_slot_counts=global_slot_counts,
            allow_cancel=allow_cancel,
            use_expected_depature=use_expected_depature
        )

        state = env.reset()
        done = False
        n_slot = len(slots)
        while not done:
            mask = get_action_mask_with_virtuals(env, flight_real_mask, slot_real_mask)
            mask_flat = mask.flatten()
            if not mask_flat.any():
                break
            with torch.no_grad():
                action_idx, log_prob, value, _ = agent.get_action(state, mask, device=device, temperature=1.0)
            flight_idx, slot_idx = decode_action(action_idx, batch_flight_size, n_slot)

            if not mask_flat[action_idx]:
                possible_idxs = np.where(mask_flat)[0]
                if len(possible_idxs) > 0:
                    action_idx = np.random.choice(possible_idxs)
                    flight_idx, slot_idx = decode_action(action_idx, batch_flight_size, n_slot)
                else:
                    break

            next_state, reward, done = env.step((flight_idx, slot_idx))
            state = next_state

        assign_df = env.get_assignment_results()
        assign_df['batch_id'] = batch_id
        assign_df = assign_df.iloc[:sum(flight_real_mask)]
        assigned_flight_ids.update(assign_df['flight_id'].tolist())
        total_results.append(assign_df)
        flight_ptr += batch_flight_size
        batch_id += 1

    final_assign_df = pd.concat(total_results, ignore_index=True)
    missing_flights = set(flights_df['flight_id']) - set(final_assign_df['flight_id'])
    if missing_flights:
        missing_rows = []
        for fid in missing_flights:
            flight_row = flights_df[flights_df['flight_id'] == fid].iloc[0]
            missing_rows.append({
                'flight_id': fid,
                'scheduled_departure': flight_row['scheduled_departure'],
                'actual_departure': flight_row['actual_departure'] if 'actual_departure' in flight_row else pd.NaT,
                'expected_departure': flight_row.get('expected_departure', flight_row.get('actual_departure', pd.NaT)),
                'passenger_count': flight_row['passenger_count'],
                'new_scheduled_departure': None,
                'new_delay_minutes': 8888,
                'new_delay_cost': 8888*flight_row.get('passenger_count', 0),
                'cancelled': True,
                'origin_delay_minutes': max(0, (flight_row.get('expected_departure', flight_row.get('actual_departure', pd.NaT)) - flight_row['scheduled_departure']).total_seconds() / 60.0 - 15) if pd.notna(flight_row.get('expected_departure', flight_row.get('actual_departure', pd.NaT))) else 0,
                'origin_delay_cost': max(0, (flight_row.get('expected_departure', flight_row.get('actual_departure', pd.NaT)) - flight_row['scheduled_departure']).total_seconds() / 60.0 - 15) * flight_row.get('passenger_count', 0) if pd.notna(flight_row.get('expected_departure', flight_row.get('actual_departure', pd.NaT))) else 0,
                'ture_new_delay': 8888,
                'ture_new_cost': 8888*flight_row.get('passenger_count', 0),
                'ture_origin_delay': max(0, (flight_row['actual_departure'] - flight_row['scheduled_departure']).total_seconds() / 60.0 - 15) if pd.notna(flight_row.get('actual_departure', pd.NaT)) else 0,
                'ture_origin_cost': max(0, (flight_row['actual_departure'] - flight_row['scheduled_departure']).total_seconds() / 60.0 - 15) * flight_row.get('passenger_count', 0) if pd.notna(flight_row.get('actual_departure', pd.NaT)) else 0,
                'batch_id': -1
            })
        final_assign_df = pd.concat([final_assign_df, pd.DataFrame(missing_rows)], ignore_index=True)

    return final_assign_df, pd.DataFrame()

# ============= 时间窗口生成 =============
def get_time_windows_for_whole_day(start_time, slot_count=72, slot_length=5):
    windows = []
    cur = start_time
    day_end = (start_time.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1))
    while cur < day_end:
        window_start = cur
        window_end = window_start + timedelta(minutes=slot_length*slot_count)
        if window_end > day_end:
            window_end = day_end
        windows.append((window_start, window_end))
        cur = window_start + timedelta(minutes=slot_length*int(slot_count/2))  # 滑窗
    return windows

# ============= 主要预测功能 =============
def whole_day_predict(
    model_path, excel_path,
    slot_length=5, max_per_slot=5, batch_flight_size=46, slot_count=72,
    allow_cancel=False
):
    """
    全天预测功能
    参数:
        model_path: 模型权重文件路径
        excel_path: 输入Excel文件路径
        slot_length: 时隙长度（分钟）
        max_per_slot: 每个时隙最大航班数
        batch_flight_size: 批处理航班数量
        slot_count: 时隙数量
        allow_cancel: 是否允许取消航班
    返回:
        df: 原始数据
        merged_assignment: 合并后的排班结果
    """
    loader = FlightDataLoader(excel_path, mode='test')
    df = loader.get_data()
    df['date_only'] = df['scheduled_departure'].dt.strftime('%Y-%m-%d')
    date0 = df['date_only'].min()
    day_start = datetime.strptime(date0 + ' 00:00:00', '%Y-%m-%d %H:%M:%S')
    windows = get_time_windows_for_whole_day(day_start, slot_count=slot_count, slot_length=slot_length)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    state_dim = batch_flight_size + slot_count
    action_dim = batch_flight_size * (slot_count + 1)
    agent = MaskedActorCritic(state_dim, action_dim).to(device)
    agent.load_state_dict(torch.load(model_path, map_location=device))
    agent.eval()

    slots_per_window = []
    for window_start, window_end in windows:
        slots = generate_time_slots(window_start, window_end, slot_length)
        slots_per_window.append(slots)
    global_slot_key2idx, idx2slot = build_global_slot_index(slots_per_window)
    global_slot_counts = [0] * len(idx2slot)

    assigned_flight_ids = set()
    all_results = []

    for i, (window_start, window_end) in enumerate(windows):
        flights = df[
            (df['scheduled_departure'] >= window_start) &
            (df['scheduled_departure'] < window_end) &
            (~df['flight_id'].isin(assigned_flight_ids))
        ].copy()
        slots = slots_per_window[i]
        slots, slot_real_mask = pad_slots_to_count(slots, slot_count, window_end)
        available_slots = filter_available_slots(df, window_start, window_end, slot_length)
        available_slots = list(available_slots) + [False]*(slot_count-len(available_slots))
        slot_occupied_counts = [0 for _ in range(slot_count)]
        if len(flights) == 0:
            continue
        res_df, _ = batch_predict(
            agent, model_path, flights, slots, available_slots, slot_occupied_counts,
            slot_length, max_per_slot,
            batch_flight_size=batch_flight_size, slot_count=slot_count, device=device,
            slot_real_mask=slot_real_mask,
            global_slot_key2idx=global_slot_key2idx,
            global_slot_counts=global_slot_counts,
            allow_cancel=allow_cancel,
            use_expected_depature=True
        )
        res_df['window_start'] = window_start
        res_df['window_end'] = window_end
        all_results.append(res_df)
        assigned_flight_ids.update(res_df['flight_id'].unique())

    if len(all_results) == 0:
        print("全天没有任何航班。")
        return df, pd.DataFrame()
    final_assignment = pd.concat(all_results, ignore_index=True)
    final_assignment = final_assignment.reset_index(drop=True)
    final_assignment = final_assignment.drop_duplicates(subset=['flight_id'], keep='last').reset_index(drop=True)

    # ======= 合并航班功能 =======
    merged_assignment = merge_flights(final_assignment)
    return df, merged_assignment

# ============= Excel处理和可视化功能 =============
def process_and_visualize_excel(df, output_excel_path):
    """
    处理排班结果并生成可视化图表
    参数:
        df: 排班结果DataFrame
        output_excel_path: 输出Excel文件路径
    """
    # 检查是否存在有效的乘客数据
    has_passenger_data = 'passenger_count' in df.columns and df['passenger_count'].sum() > 0

    if 'expected_departure' not in df.columns:
        df['expected_departure'] = df['actual_departure']
    df['slot_count']        = df.groupby('new_scheduled_departure')['new_scheduled_departure'].transform('count')
    df['origin_slot_count'] = df.groupby('scheduled_departure')['scheduled_departure'].transform('count')

    def time_str_to_minutes(ts):
        dt = pd.to_datetime(ts)
        return dt.hour * 60 + dt.minute + dt.second / 60.0

    for col in ['scheduled_departure', 'actual_departure', 'expected_departure', 'new_scheduled_departure']:
        df[col + '_min'] = df[col].apply(time_str_to_minutes)

    # delay 系列
    df['new_delay']         = (df['expected_departure_min'] - df['new_scheduled_departure_min'] - 15).clip(lower=0)
    df['origin_delay']      = (df['expected_departure_min'] - df['scheduled_departure_min'] - 15).clip(lower=0)
    df['ture_new_delay']    = (df['actual_departure_min'] - df['new_scheduled_departure_min'] - 15).clip(lower=0)
    df['ture_origin_delay'] = (df['actual_departure_min'] - df['scheduled_departure_min'] - 15).clip(lower=0)
    
    # 如果有乘客数据，则计算成本；否则成本列填充为0
    if has_passenger_data:
        df['new_delay_cost']         = df.apply(lambda x: delay_cost_caculate(x['new_delay'], x['passenger_count']), axis=1)
        df['origin_delay_cost']      = df.apply(lambda x: delay_cost_caculate(x['origin_delay'], x['passenger_count']), axis=1)
        df['ture_new_cost']          = df.apply(lambda x: delay_cost_caculate(x['ture_new_delay'], x['passenger_count']), axis=1)
        df['ture_origin_cost']       = df.apply(lambda x: delay_cost_caculate(x['ture_origin_delay'], x['passenger_count']), axis=1)
    else:
        for col in ['new_delay_cost', 'origin_delay_cost', 'ture_new_cost', 'ture_origin_cost']:
            df[col] = 0

    df['delta_delay']       = df['origin_delay'] - df['new_delay']
    df['ture_delta_delay']  = df['ture_origin_delay'] - df['ture_new_delay']
    df['delta_cost']        = df['origin_delay_cost'] - df['new_delay_cost']
    df['ture_delta_cost']   = df['ture_origin_cost'] - df['ture_new_cost']

    # 保存中间结果
    df.to_excel(output_excel_path, index=False)

    # 可视化
    sns.set_style('whitegrid')
    generated_images = []

    # slot 对比直方图
    bins = np.arange(0, 24*60+1, 5)
    df['orig_min_day'] = (pd.to_datetime(df['scheduled_departure']) - pd.to_datetime(df['scheduled_departure']).dt.normalize()).dt.total_seconds() / 60
    df['new_min_day']  = (pd.to_datetime(df['new_scheduled_departure']) - pd.to_datetime(df['new_scheduled_departure']).dt.normalize()).dt.total_seconds() / 60
    fig1, ax1 = plt.subplots(figsize=(12, 4))
    ax1.hist(df['orig_min_day'], bins=bins, alpha=0.5, label='origin_slot_count', color='steelblue')
    ax1.hist(df['new_min_day'],  bins=bins, alpha=0.5, label='slot_count', color='orange')
    ax1.set_xlabel('Time of day (minutes from 00:00)')
    ax1.set_ylabel('Number of flights')
    ax1.set_title('Slot count comparison (5-min bins)')
    ax1.legend()
    fig1.tight_layout()
    fig1.savefig('slot_count_comparison.png', dpi=150)
    generated_images.append('slot_count_comparison.png')

    # delta_delay 对比图
    fig2, ax2 = plt.subplots(figsize=(14, 4))
    x = np.arange(len(df))
    width = 0.4
    ax2.bar(x - width/2, df['delta_delay'],      width, label='delta_delay (expected)', color='seagreen')
    ax2.bar(x + width/2, df['ture_delta_delay'], width, label='ture_delta_delay (actual)', color='crimson')
    ax2.set_xlabel('Flight index')
    ax2.set_ylabel('Delay reduction (minutes)')
    ax2.set_title('Delta delay per flight')
    ax2.legend()
    fig2.tight_layout()
    fig2.savefig('delta_delay_comparison.png', dpi=150)
    generated_images.append('delta_delay_comparison.png')

    # 延误改善率
    total_origin_delay      = df['origin_delay'].sum()
    total_new_delay         = df['new_delay'].sum()
    total_ture_origin_delay = df['ture_origin_delay'].sum()
    total_ture_new_delay    = df['ture_new_delay'].sum()
    
    improve_rate_expected = ((total_origin_delay - total_new_delay) / total_origin_delay) * 100 if total_origin_delay > 0 else 0
    improve_rate_actual   = ((total_ture_origin_delay - total_ture_new_delay) / total_ture_origin_delay) * 100 if total_ture_origin_delay > 0 else 0

    summary_text = (
        f"Expected delay improvement: {total_origin_delay:.1f} → {total_new_delay:.1f} "
        f"({improve_rate_expected:.1f}%)\n"
        f"Actual   delay improvement: {total_ture_origin_delay:.1f} → {total_ture_new_delay:.1f} "
        f"({improve_rate_actual:.1f}%)"
    )

    if has_passenger_data:
        total_origin_cost      = df['origin_delay_cost'].sum()
        total_new_cost         = df['new_delay_cost'].sum()
        total_ture_origin_cost = df['ture_origin_cost'].sum()
        total_ture_new_cost    = df['ture_new_cost'].sum()
        improve_cost_expected = ((total_origin_cost - total_new_cost) / total_origin_cost) * 100 if total_origin_cost > 0 else 0
        improve_cost_actual   = ((total_ture_origin_cost - total_ture_new_cost) / total_ture_origin_cost) * 100 if total_ture_origin_cost > 0 else 0
        summary_text += (
            f"\nExpected delay cost improvement: {total_origin_cost:.1f} → {total_new_cost:.1f} "
            f"({improve_cost_expected:.1f}%)\n"
            f"Actual   delay cost improvement: {total_ture_origin_cost:.1f} → {total_ture_new_cost:.1f} "
            f"({improve_cost_actual:.1f}%)"
        )
    
    print(summary_text)

    fig3, ax3 = plt.subplots(figsize=(6, 3))
    sns.barplot(x=[improve_rate_expected, improve_rate_actual],
                y=['Expected', 'Actual'],
                palette=['seagreen', 'crimson'], ax=ax3)
    ax3.set_xlabel('Improvement Rate (%)')
    ax3.set_title('Overall Delay Improvement')
    fig3.tight_layout()
    fig3.savefig('improvement_rate.png', dpi=150)
    generated_images.append('improvement_rate.png')

    # 如果有乘客数据，则绘制延误成本对比曲线
    if has_passenger_data:
        fig4, ax4 = plt.subplots(figsize=(14,4))
        ax4.plot(df['new_delay_cost'], label='New Delay Cost (expected)', color='blue')
        ax4.plot(df['ture_new_cost'], label='New Delay Cost (actual)', color='red')
        ax4.set_title('Delay Cost per Flight')
        ax4.set_xlabel('Flight index')
        ax4.set_ylabel('Cost')
        ax4.legend()
        fig4.tight_layout()
        fig4.savefig('delay_cost_per_flight.png', dpi=150)
        generated_images.append('delay_cost_per_flight.png')

    # 插入 Excel
    wb = load_workbook(output_excel_path)
    ws = wb.create_sheet('Charts')
    ws['A1'] = summary_text.replace('\n', '  ')

    image_anchors = {
        'slot_count_comparison.png': 'A3',
        'delta_delay_comparison.png': 'A22',
        'improvement_rate.png': 'A42',
        'delay_cost_per_flight.png': 'A62'
    }

    for img_file in generated_images:
        if os.path.exists(img_file):
            img = XLImage(img_file)
            img.anchor = image_anchors[img_file]
            ws.add_image(img)

    wb.save(output_excel_path)
    print(f'所有图表已生成并插入到 Excel 的 "Charts" 工作表 ({output_excel_path})。')

# ============= 主函数 =============
def main():
    """
    主函数 - 执行预测流程
    """
    # 配置参数
    excel_path = '离港延误预测结果05-23（MAE=40.7577, RMSE=72.3417）.xlsx'
    model_path = 'ppo_model.pt'
    output_excel = '离港延误预测结果05-23_processed.xlsx'

    # 预测参数
    slot_length = 5        # 时隙长度（分钟）
    max_per_slot = 5       # 每个时隙最大航班数
    batch_flight_size = 46 # 批处理航班数量
    slot_count = 72        # 时隙数量

    print("=" * 60)
    print("航班延误预测和重排班系统")
    print("=" * 60)
    print(f"输入文件: {excel_path}")
    print(f"模型文件: {model_path}")
    print(f"输出文件: {output_excel}")
    print(f"预测参数: 时隙长度={slot_length}分钟, 最大航班/时隙={max_per_slot}, 批处理大小={batch_flight_size}")
    print("-" * 60)

    # 检查文件是否存在
    if not os.path.exists(excel_path):
        print(f"错误: 输入Excel文件不存在: {excel_path}")
        return

    if not os.path.exists(model_path):
        print(f"错误: 模型文件不存在: {model_path}")
        return

    try:
        print("开始预测...")
        # 执行全天预测
        df, assignment = whole_day_predict(
            model_path, excel_path,
            slot_length=slot_length,
            max_per_slot=max_per_slot,
            batch_flight_size=batch_flight_size,
            slot_count=slot_count
        )

        print(f"预测完成! 共处理 {len(df)} 个航班")

        # 处理和可视化结果
        if assignment is not None and not assignment.empty:
            print("开始生成可视化结果...")
            process_and_visualize_excel(assignment, output_excel)
            print(f"结果已保存到: {output_excel}")

            # 输出统计信息
            print("\n" + "=" * 60)
            print("预测结果统计:")
            print("=" * 60)
            cancelled_count = assignment['cancelled'].sum()
            total_flights = len(assignment)
            print(f"总航班数: {total_flights}")
            print(f"取消航班数: {cancelled_count}")
            print(f"取消率: {cancelled_count/total_flights:.2%}")

            if 'new_delay_minutes' in assignment.columns:
                avg_delay = assignment[~assignment['cancelled']]['new_delay_minutes'].mean()
                print(f"平均延误时间: {avg_delay:.2f} 分钟")

            if 'merge_note' in assignment.columns:
                merged_count = assignment[assignment['merge_note'] != ''].shape[0]
                print(f"合并航班数: {merged_count}")

        else:
            print("无排班结果，无需处理。")

    except Exception as e:
        print(f"预测过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

def predict_with_custom_params(excel_path, model_path, output_excel=None,
                              slot_length=5, max_per_slot=5, batch_flight_size=46, slot_count=72):
    """
    使用自定义参数进行预测

    参数:
        excel_path: 输入Excel文件路径
        model_path: 模型权重文件路径
        output_excel: 输出Excel文件路径（可选）
        slot_length: 时隙长度（分钟）
        max_per_slot: 每个时隙最大航班数
        batch_flight_size: 批处理航班数量
        slot_count: 时隙数量

    返回:
        df: 原始数据
        assignment: 排班结果
    """
    if output_excel is None:
        output_excel = excel_path.replace('.xlsx', '_predicted.xlsx')

    df, assignment = whole_day_predict(
        model_path, excel_path,
        slot_length=slot_length,
        max_per_slot=max_per_slot,
        batch_flight_size=batch_flight_size,
        slot_count=slot_count
    )

    if assignment is not None and not assignment.empty:
        process_and_visualize_excel(assignment, output_excel)

    return df, assignment

if __name__ == '__main__':
    main()
