#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一重排班模块 - 100%基于predict.py的实现
完全遵循predict.py中的算法逻辑，确保结果一致性
"""

import pandas as pd
import numpy as np
import os
import torch
import torch.nn as nn
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings("ignore", category=FutureWarning)

# ============= 从predict.py完全复制的核心函数 =============

def delay_cost_caculate(delay, passenger_count):
    return np.where(delay < 240, 0,
                    np.where(delay <= 480, 200 * passenger_count,
                             400 * passenger_count)).astype(int)

def generate_time_slots(window_start, window_end, slot_length=5):
    slots = []
    t = window_start
    while t < window_end:
        slot_end = t + timedelta(minutes=slot_length)
        slots.append({'slot_start': t, 'slot_end': slot_end})
        t = slot_end
    return slots

def get_slot_index_for_time(slots, dt):
    for idx, slot in enumerate(slots):
        if slot['slot_start'] <= dt < slot['slot_end']:
            return idx
    return None

def filter_available_slots(df, window_start, window_end, slot_length=5):
    slots = generate_time_slots(window_start, window_end, slot_length)
    available = [False] * len(slots)
    for idx, row in df.iterrows():
        sch_dep = row['scheduled_departure']
        slot_idx = get_slot_index_for_time(slots, sch_dep)
        if slot_idx is not None:
            available[slot_idx] = True
    return available

def build_global_slot_index(slots_per_window):
    slot_key2idx = dict()
    idx2slot = dict()
    idx = 0
    for slots in slots_per_window:
        for slot in slots:
            key = (slot['slot_start'], slot['slot_end'])
            if key not in slot_key2idx:
                slot_key2idx[key] = idx
                idx2slot[idx] = slot
                idx += 1
    return slot_key2idx, idx2slot

def get_time_windows_for_whole_day(start_time, slot_count=72, slot_length=5):
    windows = []
    cur = start_time
    day_end = (start_time.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1))
    while cur < day_end:
        window_start = cur
        window_end = window_start + timedelta(minutes=slot_length*slot_count)
        if window_end > day_end:
            window_end = day_end
        windows.append((window_start, window_end))
        cur = window_start + timedelta(minutes=slot_length*int(slot_count/2))
    return windows

def pad_flights_to_batch(flights_df, batch_size, slot_count):
    n = len(flights_df)
    if n >= batch_size:
        df = flights_df.iloc[:batch_size].copy()
        real_mask = np.ones(batch_size, dtype=bool)
        return df, real_mask
    else:
        df_real = flights_df.copy()
        virtual_rows = []
        max_sch_dep = df_real['scheduled_departure'].max() if not df_real.empty else datetime.now()

        for i in range(batch_size - n):
            virtual_row = {
                'flight_id': f'virtual_{i}',
                'scheduled_departure': max_sch_dep + timedelta(minutes=5*(i+1)),
                'actual_departure': max_sch_dep + timedelta(minutes=10*(i+1)),
                'expected_departure': max_sch_dep + timedelta(minutes=10*(i+1)),
                'passenger_count': 0,
                'passenger_count_max': 0,
                'arrival_place': ''
            }
            virtual_rows.append(virtual_row)
        df_virtual = pd.DataFrame(virtual_rows)
        df = pd.concat([df_real, df_virtual], ignore_index=True)
        real_mask = np.array([True]*n + [False]*(batch_size-n))
        return df, real_mask

def pad_slots_to_count(slots, slot_count, window_end):
    n = len(slots)
    if n >= slot_count:
        return slots, np.ones(slot_count, dtype=bool)
    else:
        virtual_slots = []
        t = slots[-1]['slot_end'] if n > 0 else window_end
        for i in range(slot_count - n):
            slot_start = t + timedelta(minutes=5*i)
            slot_end = slot_start + timedelta(minutes=5)
            virtual_slots.append({'slot_start': slot_start, 'slot_end': slot_end})
        all_slots = slots + virtual_slots
        real_mask = np.array([True]*n + [False]*(slot_count-n))
        return all_slots, real_mask

def get_action_mask_with_virtuals(env, flight_real_mask, slot_real_mask):
    n_flight, n_slot = len(env.flights_df), len(env.slots)
    mask = env.get_action_mask()
    for i, is_real_flight in enumerate(flight_real_mask):
        if not is_real_flight:
            mask[i, :] = False
    for j, is_real_slot in enumerate(slot_real_mask):
        if not is_real_slot:
            mask[:, j] = False
    return mask

def flatten_state(state):
    return np.array(state[0] + state[1], dtype=np.float32)

def decode_action(action_idx, n_flight, n_slot):
    slot_plus = n_slot + 1
    flight_idx = action_idx // slot_plus
    slot_idx = action_idx % slot_plus
    return (flight_idx, slot_idx)

# ============= 神经网络模型（与predict.py完全一致）=============
class MaskedActorCritic(nn.Module):
    def __init__(self, state_dim, action_dim, hidden_dim=128):
        super(MaskedActorCritic, self).__init__()
        self.fc1 = nn.Linear(state_dim, hidden_dim)
        self.fc2 = nn.Linear(hidden_dim, hidden_dim)
        self.actor_head = nn.Linear(hidden_dim, action_dim)
        self.critic_head = nn.Linear(hidden_dim, 1)

    def forward(self, x):
        x = torch.relu(self.fc1(x))
        x = torch.relu(self.fc2(x))
        logits = self.actor_head(x)
        value = self.critic_head(x)
        return logits, value

    def get_action(self, state, action_mask, device='cpu', temperature=2.0):
        state_tensor = torch.tensor(flatten_state(state), dtype=torch.float32).unsqueeze(0).to(device)
        logits, value = self.forward(state_tensor)
        mask_tensor = torch.tensor(action_mask.flatten(), dtype=torch.bool).to(device)
        logits = logits.masked_fill(~mask_tensor, float('-inf'))
        probs = torch.softmax(logits / temperature, dim=-1)
        dist = torch.distributions.Categorical(probs)
        action_idx = dist.sample().item()
        log_prob = dist.log_prob(torch.tensor(action_idx).to(device)).item()
        return action_idx, log_prob, value.item(), probs.cpu().detach().numpy()

# ============= 环境类（与predict.py完全一致）=============
class FlightRecoveryEnv:
    def __init__(
        self, flights_df, slots,
        available_slots, slot_length=5, max_per_slot=5,
        global_slot_key2idx=None, global_slot_counts=None,
        slot_reward_pos=1.0, slot_reward_neg=-0.7,
        cancel_penalty=-2000,
        allow_cancel=True, 
        use_expected_depature=False   
    ):
        self.flights_df = flights_df.reset_index(drop=True)
        self.slots = slots
        self.available_slots = available_slots
        self.slot_length = slot_length
        self.max_per_slot = max_per_slot

        self.slot_counts = [0 for _ in self.slots]
        self.flight_assignments = [-1 for _ in range(len(self.flights_df))]
        self.state = (tuple(self.flight_assignments), tuple(self.slot_counts))
        self.done = False

        self.global_slot_key2idx = global_slot_key2idx
        self.global_slot_counts = global_slot_counts

        self.use_expected_depature = use_expected_depature

        self.original_delays = []
        for idx, flight in self.flights_df.iterrows():
            sch_dep = flight['scheduled_departure']
            if self.use_expected_depature and 'expected_departure' in flight:
                actual_dep = flight['expected_departure']
            else:
                actual_dep = flight['actual_departure']
            delay = (actual_dep - sch_dep).total_seconds() / 60.0 - 15
            self.original_delays.append(max(delay, 0))

        self.slot_reward_pos = slot_reward_pos
        self.slot_reward_neg = slot_reward_neg
        self.cancel_penalty = cancel_penalty
        self.allow_cancel = allow_cancel  

    def get_action_mask(self):
        n_flight = len(self.flights_df)
        n_slot = len(self.slots)
        mask = np.zeros((n_flight, n_slot + 1), dtype=np.bool_)
        for idx, assigned in enumerate(self.flight_assignments):
            if assigned == -1:
                flight = self.flights_df.iloc[idx]
                sch_dep = flight['scheduled_departure']
                if self.use_expected_depature and 'expected_departure' in flight:
                    actual_dep = flight['expected_departure']
                else:
                    actual_dep = flight['actual_departure']
                for slot_idx, slot in enumerate(self.slots):
                    if slot['slot_start'] > actual_dep  + timedelta(minutes=20):
                        continue
                    if not self.available_slots[slot_idx]:
                        continue
                    if self.get_slot_remain_capacity(slot_idx) <= 0:
                        continue
                    if slot['slot_start'] < sch_dep-timedelta(minutes=10):
                        continue
                    mask[idx, slot_idx] = True
                if self.allow_cancel:
                    mask[idx, n_slot] = True
                else:
                    mask[idx, n_slot] = False
        return mask

    def get_slot_remain_capacity(self, slot_idx):
        cap_local = self.slot_counts[slot_idx]
        if (self.global_slot_key2idx is not None) and (self.global_slot_counts is not None):
            key = (self.slots[slot_idx]['slot_start'], self.slots[slot_idx]['slot_end'])
            global_idx = self.global_slot_key2idx.get(key, None)
            cap_global = self.global_slot_counts[global_idx] if global_idx is not None else 0
            return self.max_per_slot - (cap_local + cap_global)
        else:
            return self.max_per_slot - cap_local

    def reset(self):
        self.slot_counts = [0 for _ in self.slots]
        self.flight_assignments = [-1 for _ in range(len(self.flights_df))]
        self.state = (tuple(self.flight_assignments), tuple(self.slot_counts))
        self.done = False
        return self.state

    def step(self, action):
        flight_idx, slot_idx = action
        if self.flight_assignments[flight_idx] != -1:
            raise Exception(f"航班{flight_idx}已分配，不可重复分配")
        n_slot = len(self.slots)
        flight = self.flights_df.iloc[flight_idx]
        sch_dep = flight['scheduled_departure']
        if self.use_expected_depature and 'expected_departure' in flight:
            actual_dep = flight['expected_departure']
        else:
            actual_dep = flight['actual_departure']
        passenger_count = flight['passenger_count']
        original_delay = self.original_delays[flight_idx]

        # 取消动作
        if slot_idx == n_slot:
            self.flight_assignments[flight_idx] = -2  # -2表示取消
            reward = self.cancel_penalty
            done = all([x != -1 for x in self.flight_assignments])
            self.done = done
            self.state = (tuple(self.flight_assignments), tuple(self.slot_counts))
            return self.state, reward, done

        prev_slot_count = self.slot_counts[slot_idx]
        self.flight_assignments[flight_idx] = slot_idx
        self.slot_counts[slot_idx] += 1
        new_sch_departure = self.slots[slot_idx]['slot_start']
        if (self.global_slot_key2idx is not None) and (self.global_slot_counts is not None):
            key = (self.slots[slot_idx]['slot_start'], self.slots[slot_idx]['slot_end'])
            global_idx = self.global_slot_key2idx.get(key, None)
            if global_idx is not None:
                self.global_slot_counts[global_idx] += 1

        delay = (actual_dep - new_sch_departure).total_seconds() / 60.0 -15
        delay = max(delay, 0)

        reward_delay = -(delay - original_delay) / 100.0

        if delay < 240:
            delay_cost = 0
        elif delay <= 480 :
            delay_cost = int(200 * passenger_count)
        else:
            delay_cost = int(400 * passenger_count)
        if original_delay < 240:
            original_delay_cost = 0
        elif original_delay <= 480 :
            original_delay_cost = int(200 * passenger_count)
        else:
            original_delay_cost = int(400 * passenger_count)

        reward_cost = -(delay_cost - original_delay_cost) / 2000.0

        slot_use_reward = 0
        if prev_slot_count == 0:
            slot_use_reward = self.slot_reward_pos
        elif prev_slot_count in [1, 2, 3, 4]:
            slot_use_reward = self.slot_reward_neg

        reward = reward_delay + reward_cost + slot_use_reward

        done = all([x != -1 for x in self.flight_assignments])
        self.done = done
        self.state = (tuple(self.flight_assignments), tuple(self.slot_counts))
        return self.state, reward, done

    def get_assignment_results(self, use_actual_for_stats=False):
        results = []
        n_slot = len(self.slots)
        for idx, slot_idx in enumerate(self.flight_assignments):
            flight = self.flights_df.iloc[idx]
            result = {
                'flight_id': flight['flight_id'],
                'scheduled_departure': flight['scheduled_departure'],
                'actual_departure': flight['actual_departure'] if 'actual_departure' in flight else None,
                'expected_departure': flight['expected_departure'] if 'expected_departure' in flight else None,
                'passenger_count': flight['passenger_count']
            }
            sch_dep = flight['scheduled_departure']
            if use_actual_for_stats and 'actual_departure' in flight and 'expected_departure' in flight:
                actual_dep = flight['actual_departure']
                expected_dep = flight['expected_departure']
            elif 'expected_departure' in flight:
                actual_dep = flight['expected_departure']
                expected_dep = flight['expected_departure']
            else:
                actual_dep = flight['actual_departure']
                expected_dep = flight.get('expected_departure', None) or flight['actual_departure']

            if slot_idx == -1:
                result['new_scheduled_departure'] = sch_dep - timedelta(minutes=5)
                result['new_delay_minutes'] = max(0,(expected_dep - sch_dep).total_seconds() / 60.0 -15)
                result['new_delay_cost'] = result['new_delay_minutes'] * flight['passenger_count']
                result['cancelled'] = False
                result['origin_delay_minutes'] = max(0,(expected_dep - sch_dep).total_seconds() / 60.0 -15)
                result['origin_delay_cost'] = delay_cost_caculate(result['origin_delay_minutes'],flight['passenger_count'])
                result['ture_new_delay'] = max(0,(actual_dep - sch_dep).total_seconds() / 60.0 -15)
                result['ture_new_cost'] = result['ture_new_delay'] * flight['passenger_count']
                result['ture_origin_delay'] = max(0,(actual_dep - sch_dep).total_seconds() / 60.0 -15)
                result['ture_origin_cost'] = delay_cost_caculate(result['ture_origin_delay'] , flight['passenger_count'])
            elif slot_idx == n_slot or slot_idx == -2:
                result['new_scheduled_departure'] = None
                result['new_delay_minutes'] = 9999
                result['new_delay_cost'] = 9999 * flight['passenger_count']
                result['cancelled'] = True
                result['origin_delay_minutes'] = max(0,(expected_dep - sch_dep).total_seconds() / 60.0 -15)
                result['origin_delay_cost'] = result['origin_delay_minutes'] * flight['passenger_count']
                result['ture_new_delay'] = 9999
                result['ture_new_cost'] = 9999 * flight['passenger_count']
                result['ture_origin_delay'] = max(0,(actual_dep - sch_dep).total_seconds() / 60.0 -15)
                result['ture_origin_cost'] = delay_cost_caculate(result['ture_origin_delay'] , flight['passenger_count'])
            else:
                new_sch_dep = self.slots[slot_idx]['slot_start']
                delay = (expected_dep - new_sch_dep).total_seconds() / 60.0 -15
                delay = max(delay, 0)
                result['new_scheduled_departure'] = new_sch_dep
                result['new_delay_minutes'] = delay
                result['new_delay_cost'] = delay_cost_caculate(delay , flight['passenger_count'])
                result['cancelled'] = False
                result['origin_delay_minutes'] = max(0,(expected_dep - sch_dep).total_seconds() / 60.0 -15)
                result['origin_delay_cost'] = delay_cost_caculate(result['origin_delay_minutes'] , flight['passenger_count'])
                delay_true = (actual_dep - new_sch_dep).total_seconds() / 60.0 -15
                delay_true = max(delay_true, 0)
                result['ture_new_delay'] = delay_true
                result['ture_new_cost'] = delay_cost_caculate(delay_true , flight['passenger_count'])
                result['ture_origin_delay'] = max(0,(actual_dep - sch_dep).total_seconds() / 60.0 -15)
                result['ture_origin_cost'] = delay_cost_caculate(result['ture_origin_delay'] , flight['passenger_count'])
            results.append(result)
        return pd.DataFrame(results)

# ============= 批处理预测功能（与predict.py完全一致）=============
def batch_predict(
    agent, model_path,
    flights_df, slots, available_slots, slot_occupied_counts,
    slot_length, max_per_slot,
    batch_flight_size=46, slot_count=72, device='cpu',
    slot_real_mask=None,
    global_slot_key2idx=None, global_slot_counts=None,
    allow_cancel=True,
    use_expected_depature=False
):
    total_results = []
    n_flight = len(flights_df)
    flight_ptr = 0
    batch_id = 0
    unassigned_flights = []
    all_flight_ids = set(flights_df['flight_id'])
    assigned_flight_ids = set()

    while flight_ptr < n_flight:
        batch_df = flights_df.iloc[flight_ptr:flight_ptr+batch_flight_size]
        batch_df, flight_real_mask = pad_flights_to_batch(batch_df, batch_flight_size, slot_count)
        env = FlightRecoveryEnv(
            batch_df, slots, available_slots, slot_length=slot_length, max_per_slot=max_per_slot,
            global_slot_key2idx=global_slot_key2idx,
            global_slot_counts=global_slot_counts,
            allow_cancel=allow_cancel,
            use_expected_depature=use_expected_depature
        )

        state = env.reset()
        done = False
        n_slot = len(slots)
        while not done:
            mask = get_action_mask_with_virtuals(env, flight_real_mask, slot_real_mask)
            mask_flat = mask.flatten()
            if not mask_flat.any():
                break
            with torch.no_grad():
                action_idx, log_prob, value, _ = agent.get_action(state, mask, device=device, temperature=1.0)
            flight_idx, slot_idx = decode_action(action_idx, batch_flight_size, n_slot)

            if not mask_flat[action_idx]:
                possible_idxs = np.where(mask_flat)[0]
                if len(possible_idxs) > 0:
                    action_idx = np.random.choice(possible_idxs)
                    flight_idx, slot_idx = decode_action(action_idx, batch_flight_size, n_slot)
                else:
                    break

            next_state, reward, done = env.step((flight_idx, slot_idx))
            state = next_state

        assign_df = env.get_assignment_results()
        assign_df['batch_id'] = batch_id
        assign_df = assign_df.iloc[:sum(flight_real_mask)]
        assigned_flight_ids.update(assign_df['flight_id'].tolist())
        total_results.append(assign_df)
        flight_ptr += batch_flight_size
        batch_id += 1

    final_assign_df = pd.concat(total_results, ignore_index=True)
    missing_flights = set(flights_df['flight_id']) - set(final_assign_df['flight_id'])
    if missing_flights:
        missing_rows = []
        for fid in missing_flights:
            flight_row = flights_df[flights_df['flight_id'] == fid].iloc[0]
            missing_rows.append({
                'flight_id': fid,
                'scheduled_departure': flight_row['scheduled_departure'],
                'actual_departure': flight_row['actual_departure'] if 'actual_departure' in flight_row else pd.NaT,
                'expected_departure': flight_row.get('expected_departure', flight_row.get('actual_departure', pd.NaT)),
                'passenger_count': flight_row['passenger_count'],
                'new_scheduled_departure': None,
                'new_delay_minutes': 8888,
                'new_delay_cost': 8888*flight_row.get('passenger_count', 0),
                'cancelled': True,
                'origin_delay_minutes': max(0, (flight_row.get('expected_departure', flight_row.get('actual_departure', pd.NaT)) - flight_row['scheduled_departure']).total_seconds() / 60.0 - 15) if pd.notna(flight_row.get('expected_departure', flight_row.get('actual_departure', pd.NaT))) else 0,
                'origin_delay_cost': max(0, (flight_row.get('expected_departure', flight_row.get('actual_departure', pd.NaT)) - flight_row['scheduled_departure']).total_seconds() / 60.0 - 15) * flight_row.get('passenger_count', 0) if pd.notna(flight_row.get('expected_departure', flight_row.get('actual_departure', pd.NaT))) else 0,
                'ture_new_delay': 8888,
                'ture_new_cost': 8888*flight_row.get('passenger_count', 0),
                'ture_origin_delay': max(0, (flight_row['actual_departure'] - flight_row['scheduled_departure']).total_seconds() / 60.0 - 15) if pd.notna(flight_row.get('actual_departure', pd.NaT)) else 0,
                'ture_origin_cost': max(0, (flight_row['actual_departure'] - flight_row['scheduled_departure']).total_seconds() / 60.0 - 15) * flight_row.get('passenger_count', 0) if pd.notna(flight_row.get('actual_departure', pd.NaT)) else 0,
                'batch_id': -1
            })
        final_assign_df = pd.concat([final_assign_df, pd.DataFrame(missing_rows)], ignore_index=True)

    return final_assign_df, pd.DataFrame()

# ============= 航班合并功能（与predict.py完全一致）=============
def merge_flights(assignment: pd.DataFrame) -> pd.DataFrame:
    df = assignment.copy()
    df['merge_note'] = ""
    df['merged_to'] = None

    required_cols = ['cancelled', 'passenger_count_max', 'arrival_place']
    if not all(col in df.columns for col in required_cols):
        print("Warning: Required columns for merging not found in assignment DataFrame.")
        return df

    valid_flights = df[(~df['cancelled']) & (df['passenger_count_max'].notna()) & (df['arrival_place'] != "")]
    merged_A = set()

    for idx_a, row_a in valid_flights.iterrows():
        passenger_count_max_a = row_a.get('passenger_count_max', 0)
        passenger_count_a = row_a.get('passenger_count', 0)

        if passenger_count_max_a <= 0 or (passenger_count_a / passenger_count_max_a >= 0.5):
            continue
        if ('new_delay_minutes' not in row_a) or (row_a['new_delay_minutes'] <= 200):
            continue
        if row_a['flight_id'] in merged_A:
            continue

        candidates = valid_flights[
            (valid_flights['arrival_place'] == row_a['arrival_place']) &
            (valid_flights['flight_id'] != row_a['flight_id']) &
            (~valid_flights['flight_id'].isin(merged_A))
        ].copy()

        for idx_b, row_b in candidates.iterrows():
            passenger_count_max_b = row_b.get('passenger_count_max', 0)
            passenger_count_b = row_b.get('passenger_count', 0)
            total_passengers = passenger_count_a + passenger_count_b

            if passenger_count_max_b <= 0 or total_passengers > passenger_count_max_b:
                continue
            df.loc[idx_a, 'cancelled'] = True
            df.loc[idx_a, 'merge_note'] = f"Merged to {row_b['flight_id']}"
            df.loc[idx_a, 'merged_to'] = row_b['flight_id']
            scheduled_departure_a = pd.to_datetime(row_a['scheduled_departure'])
            new_scheduled_departure_b = pd.to_datetime(row_b['new_scheduled_departure'])
            if pd.notna(scheduled_departure_a) and pd.notna(new_scheduled_departure_b):
                delay_minutes = (new_scheduled_departure_b - scheduled_departure_a).total_seconds()/60.0 - 15
                df.loc[idx_a, 'new_delay_cost'] = passenger_count_a * max(0, delay_minutes)
                df.loc[idx_a, 'new_delay_minutes'] = max(0, delay_minutes)
            else:
                df.loc[idx_a, 'new_delay_cost'] = 0
                df.loc[idx_a, 'new_delay_minutes'] = 0

            for field in ['ture_new_cost', 'ture_new_delay']:
                if (field in df.columns) and ('actual_departure' in row_b):
                    actual_departure_b = pd.to_datetime(row_b['actual_departure'])
                    if pd.notna(scheduled_departure_a) and pd.notna(actual_departure_b):
                        delay_minutes_true = (actual_departure_b - scheduled_departure_a).total_seconds()/60.0 - 15
                        df.loc[idx_a, field] = passenger_count_a * max(0, delay_minutes_true)
                    else:
                        df.loc[idx_a, field] = 0

            merged_A.add(row_a['flight_id'])
            break

    return df

# ============= 全天预测功能（与predict.py完全一致）=============
def whole_day_predict(
    model_path, flights_df,
    slot_length=5, max_per_slot=5, batch_flight_size=46, slot_count=72,
    allow_cancel=False
):
    df = flights_df.copy()
    df['date_only'] = df['scheduled_departure'].dt.strftime('%Y-%m-%d')
    date0 = df['date_only'].min()
    day_start = datetime.strptime(date0 + ' 00:00:00', '%Y-%m-%d %H:%M:%S')
    windows = get_time_windows_for_whole_day(day_start, slot_count=slot_count, slot_length=slot_length)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    state_dim = batch_flight_size + slot_count
    action_dim = batch_flight_size * (slot_count + 1)
    agent = MaskedActorCritic(state_dim, action_dim).to(device)
    agent.load_state_dict(torch.load(model_path, map_location=device))
    agent.eval()

    slots_per_window = []
    for window_start, window_end in windows:
        slots = generate_time_slots(window_start, window_end, slot_length)
        slots_per_window.append(slots)
    global_slot_key2idx, idx2slot = build_global_slot_index(slots_per_window)
    global_slot_counts = [0] * len(idx2slot)

    assigned_flight_ids = set()
    all_results = []

    for i, (window_start, window_end) in enumerate(windows):
        flights = df[
            (df['scheduled_departure'] >= window_start) &
            (df['scheduled_departure'] < window_end) &
            (~df['flight_id'].isin(assigned_flight_ids))
        ].copy()
        slots = slots_per_window[i]
        slots, slot_real_mask = pad_slots_to_count(slots, slot_count, window_end)
        available_slots = filter_available_slots(df, window_start, window_end, slot_length)
        available_slots = list(available_slots) + [False]*(slot_count-len(available_slots))
        slot_occupied_counts = [0 for _ in range(slot_count)]
        if len(flights) == 0:
            continue
        res_df, _ = batch_predict(
            agent, model_path, flights, slots, available_slots, slot_occupied_counts,
            slot_length, max_per_slot,
            batch_flight_size=batch_flight_size, slot_count=slot_count, device=device,
            slot_real_mask=slot_real_mask,
            global_slot_key2idx=global_slot_key2idx,
            global_slot_counts=global_slot_counts,
            allow_cancel=allow_cancel,
            use_expected_depature=True
        )
        res_df['window_start'] = window_start
        res_df['window_end'] = window_end
        all_results.append(res_df)
        assigned_flight_ids.update(res_df['flight_id'].unique())

    if len(all_results) == 0:
        print("全天没有任何航班。")
        return df, pd.DataFrame()
    final_assignment = pd.concat(all_results, ignore_index=True)
    final_assignment = final_assignment.reset_index(drop=True)
    final_assignment = final_assignment.drop_duplicates(subset=['flight_id'], keep='last').reset_index(drop=True)

    merged_assignment = merge_flights(final_assignment)
    return df, merged_assignment

# ============= 统一重排班器类 =============
class UnifiedFlightRescheduler:
    """统一重排班器 - 100%基于predict.py的实现"""

    def __init__(self):
        self.model_path = os.path.join(os.path.dirname(__file__), '..', 'model', 'ppo_model.pt')
        print(f"统一重排班器初始化，模型路径: {self.model_path}")

    def process_delay_prediction_data(self, delay_prediction_df):
        """将延误预测结果转换为重排班算法需要的格式"""
        try:
            print("开始数据转换...")
            print(f"输入数据列: {list(delay_prediction_df.columns)}")
            print(f"输入数据样本:\n{delay_prediction_df.head()}")

            rescheduler_data = []

            for _, row in delay_prediction_df.iterrows():
                # 处理时间字段 - 根据实际的延误预测数据格式
                scheduled_dep = self._ensure_datetime_format(row.get('计划离港时间', ''), row.get('日期', ''))
                actual_dep = self._ensure_datetime_format(row.get('实际离港时间', ''), row.get('日期', ''))
                predicted_dep = self._ensure_datetime_format(row.get('预测离港时间', ''), row.get('日期', ''))

                # 如果没有预测时间，根据延误分钟数计算
                if not predicted_dep and '预测离港延误(分钟)' in row:
                    delay_minutes = row.get('预测离港延误(分钟)', 0)
                    if scheduled_dep and not pd.isna(delay_minutes):
                        predicted_dep = scheduled_dep + timedelta(minutes=float(delay_minutes))

                # 确保时间字段的完整性
                if not predicted_dep:
                    predicted_dep = actual_dep if actual_dep else scheduled_dep
                if not actual_dep:
                    actual_dep = predicted_dep if predicted_dep else scheduled_dep

                rescheduler_data.append({
                    'flight_id': str(row.get('航班号', '')),
                    'scheduled_departure': scheduled_dep,
                    'actual_departure': actual_dep,
                    'expected_departure': predicted_dep,
                    'passenger_count': 150,  # 默认乘客数
                    'passenger_count_max': 180,  # 默认最大乘客数
                    'arrival_place': ''  # 暂时为空
                })

            df = pd.DataFrame(rescheduler_data)

            # 确保所有必需的列都存在
            cols_needed = ['flight_id', 'scheduled_departure', 'actual_departure', 'expected_departure',
                          'passenger_count', 'passenger_count_max', 'arrival_place']
            for col in cols_needed:
                if col not in df.columns:
                    if col == 'passenger_count_max':
                        df[col] = 180
                    elif col == 'passenger_count':
                        df[col] = 150
                    elif col == 'arrival_place':
                        df[col] = ""
                    else:
                        df[col] = None

            # 过滤掉无效的数据
            df = df.dropna(subset=['flight_id', 'scheduled_departure'])
            df = df[df['flight_id'] != '']

            print(f"延误预测数据转换完成: {len(df)} 条记录")
            print(f"转换后数据列: {list(df.columns)}")
            print(f"转换后数据样本:\n{df.head()}")

            return df

        except Exception as e:
            print(f"数据转换失败: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()

    def _ensure_datetime_format(self, time_value, date_value=None):
        """确保时间格式为datetime对象"""
        if pd.isna(time_value) or time_value == '':
            return None

        if isinstance(time_value, str):
            try:
                if len(time_value) == 5 and ':' in time_value:  # HH:MM格式
                    # 如果有日期值，使用日期值；否则使用今天
                    if date_value and not pd.isna(date_value):
                        if isinstance(date_value, str):
                            date_str = date_value
                        else:
                            date_str = str(date_value)[:10]  # 取前10个字符作为日期
                    else:
                        date_str = datetime.now().strftime('%Y-%m-%d')
                    time_value = f"{date_str} {time_value}:00"
                return pd.to_datetime(time_value)
            except Exception as e:
                print(f"时间转换错误: {e}, time_value: {time_value}, date_value: {date_value}")
                return None
        elif isinstance(time_value, datetime):
            return time_value
        else:
            try:
                return pd.to_datetime(time_value)
            except:
                return None

    def optimize_schedule(self, delay_prediction_df, target_date):
        """执行航班重排班优化（完全基于predict.py的whole_day_predict函数）"""
        if not os.path.exists(self.model_path):
            raise ValueError(f"重排班模型文件不存在: {self.model_path}")

        # 转换数据格式
        processed_data = self.process_delay_prediction_data(delay_prediction_df)

        if processed_data.empty:
            raise ValueError("没有有效的延误预测数据")

        try:
            # 调用核心优化算法（完全基于predict.py的whole_day_predict）
            original_df, optimized_df = whole_day_predict(
                self.model_path, processed_data,
                slot_length=5, max_per_slot=5, batch_flight_size=46, slot_count=72,
                allow_cancel=False
            )

            print(f"重排班优化完成: 原始 {len(original_df)} 条，优化后 {len(optimized_df) if optimized_df is not None else 0} 条")

            # 转换为显示格式
            if optimized_df is not None and not optimized_df.empty:
                result_df = self._convert_to_display_format(optimized_df)
            else:
                result_df = pd.DataFrame()

            return original_df, result_df

        except Exception as e:
            print(f"重排班优化失败: {e}")
            import traceback
            traceback.print_exc()
            return processed_data, None

    def _convert_to_display_format(self, assignment_df):
        """转换为显示格式"""
        if assignment_df.empty:
            return pd.DataFrame()

        result_data = []
        for _, row in assignment_df.iterrows():
            result_data.append({
                'flight_id': row['flight_id'],
                'scheduled_departure': row['scheduled_departure'],
                'actual_departure': row.get('actual_departure', ''),
                'expected_departure': row.get('expected_departure', ''),
                'optimized_departure': row.get('new_scheduled_departure', row.get('scheduled_departure', '')),
                'delay_reduction': 0  # 可以根据实际情况计算
            })

        return pd.DataFrame(result_data)
