#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
挑战杯航班流量预测与运行状态推演系统
主程序入口文件
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.main_window import MainWindow
except ImportError as e:
    print(f"Import error: {e}")
    sys.exit(1)

def main():
    """主函数"""
    try:
        app = QApplication(sys.argv)

        # 设置应用程序属性
        app.setApplicationName("航班流量预测与运行状态推演系统")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("挑战杯项目组")

        # 设置全局字体
        try:
            font = QFont("Microsoft YaHei", 9)
            app.setFont(font)
        except:
            pass  # 如果字体不可用，使用默认字体

        # 设置高DPI支持
        try:
            app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
            app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        except:
            pass  # 如果不支持，忽略

        # 创建主窗口
        window = MainWindow()
        window.show()

        print("应用程序启动成功！")

        # 运行应用程序
        sys.exit(app.exec_())

    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == "__main__":
    main()
